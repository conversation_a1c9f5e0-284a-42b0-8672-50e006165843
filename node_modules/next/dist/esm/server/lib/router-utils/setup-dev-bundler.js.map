{"version": 3, "sources": ["../../../../src/server/lib/router-utils/setup-dev-bundler.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../config-shared'\nimport type { FilesystemDynamicRoute } from './filesystem'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport {\n  getPageStaticInfo,\n  type MiddlewareMatcher,\n} from '../../../build/analysis/get-page-static-info'\nimport type { RoutesManifest } from '../../../build'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { PropagateToWorkersField } from './types'\nimport type { NextJsHotReloaderInterface } from '../../dev/hot-reloader-types'\n\nimport { createDefineEnv } from '../../../build/swc'\nimport fs from 'fs'\nimport { mkdir } from 'fs/promises'\nimport url from 'url'\nimport path from 'path'\nimport qs from 'querystring'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport { loadEnvConfig } from '@next/env'\nimport findUp from 'next/dist/compiled/find-up'\nimport { buildCustomRoute } from './filesystem'\nimport * as Log from '../../../build/output/log'\nimport HotReloaderWebpack from '../../dev/hot-reloader-webpack'\nimport { setGlobal } from '../../../trace/shared'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { createValidFileMatcher } from '../find-page-file'\nimport {\n  EVENT_BUILD_FEATURE_USAGE,\n  eventCliSession,\n} from '../../../telemetry/events'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport {\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from '../../../build/entries'\nimport { verifyTypeScriptSetup } from '../../../lib/verify-typescript-setup'\nimport { verifyPartytownSetup } from '../../../lib/verify-partytown-setup'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { buildDataRoute } from './build-data-route'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { createClientRouterFilter } from '../../../lib/create-client-router-filter'\nimport { absolutePathToPage } from '../../../shared/lib/page-path/absolute-path-to-page'\nimport { generateInterceptionRoutesRewrites } from '../../../lib/generate-interception-routes-rewrites'\n\nimport {\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  PHASE_DEVELOPMENT_SERVER,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  ROUTES_MANIFEST,\n  PRERENDER_MANIFEST,\n} from '../../../shared/lib/constants'\n\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport {\n  isMiddlewareFile,\n  NestedMiddlewareError,\n  isInstrumentationHookFile,\n  getPossibleMiddlewareFilenames,\n  getPossibleInstrumentationHookFilenames,\n} from '../../../build/utils'\nimport { devPageFiles } from '../../../build/webpack/plugins/next-types-plugin/shared'\nimport type { LazyRenderServerInstance } from '../router-server'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../dev/hot-reloader-types'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { createHotReloaderTurbopack } from '../../dev/hot-reloader-turbopack'\nimport { generateEncryptionKeyBase64 } from '../../app-render/encryption-utils-server'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport { createEnvDefinitions } from '../experimental/create-env-definitions'\nimport { JsConfigPathsPlugin } from '../../../build/webpack/plugins/jsconfig-paths-plugin'\nimport { store as consoleStore } from '../../../build/output/store'\nimport {\n  isPersistentCachingEnabled,\n  ModuleBuildError,\n  TurbopackInternalError,\n} from '../../../shared/lib/turbopack/utils'\nimport { getDefineEnv } from '../../../build/define-env'\nimport { normalizePath } from '../../../lib/normalize-path'\n\nexport type SetupOpts = {\n  renderServer: LazyRenderServerInstance\n  dir: string\n  turbo?: boolean\n  appDir?: string\n  pagesDir?: string\n  telemetry: Telemetry\n  isCustomServer?: boolean\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >\n  nextConfig: NextConfigComplete\n  port: number\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  resetFetch: () => void\n}\n\nexport interface DevRoutesManifest {\n  version: number\n  caseSensitive: RoutesManifest['caseSensitive']\n  basePath: RoutesManifest['basePath']\n  rewrites: RoutesManifest['rewrites']\n  redirects: RoutesManifest['redirects']\n  headers: RoutesManifest['headers']\n  i18n: RoutesManifest['i18n']\n  skipMiddlewareUrlNormalize: RoutesManifest['skipMiddlewareUrlNormalize']\n}\n\nexport type ServerFields = {\n  actualMiddlewareFile?: string | undefined\n  actualInstrumentationHookFile?: string | undefined\n  appPathRoutes?: Record<string, string | string[]>\n  middleware?:\n    | {\n        page: string\n        match: MiddlewareRouteMatch\n        matchers?: MiddlewareMatcher[]\n      }\n    | undefined\n  hasAppNotFound?: boolean\n  interceptionRoutes?: ReturnType<\n    typeof import('./filesystem').buildCustomRoute\n  >[]\n  setIsrStatus?: (key: string, value: boolean) => void\n  resetFetch?: () => void\n}\n\nasync function verifyTypeScript(opts: SetupOpts) {\n  let usingTypeScript = false\n  const verifyResult = await verifyTypeScriptSetup({\n    dir: opts.dir,\n    distDir: opts.nextConfig.distDir,\n    intentDirs: [opts.pagesDir, opts.appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: opts.nextConfig.typescript.tsconfigPath,\n    disableStaticImages: opts.nextConfig.images.disableStaticImages,\n    hasAppDir: !!opts.appDir,\n    hasPagesDir: !!opts.pagesDir,\n  })\n\n  if (verifyResult.version) {\n    usingTypeScript = true\n  }\n  return usingTypeScript\n}\n\nexport async function propagateServerField(\n  opts: SetupOpts,\n  field: PropagateToWorkersField,\n  args: any\n) {\n  await opts.renderServer?.instance?.propagateServerField(opts.dir, field, args)\n}\n\nasync function startWatcher(\n  opts: SetupOpts & {\n    isSrcDir: boolean\n  }\n) {\n  const { nextConfig, appDir, pagesDir, dir, resetFetch } = opts\n  const { useFileSystemPublicRoutes } = nextConfig\n  const usingTypeScript = await verifyTypeScript(opts)\n\n  const distDir = path.join(opts.dir, opts.nextConfig.distDir)\n\n  // we ensure the types directory exists here\n  if (usingTypeScript) {\n    const distTypesDir = path.join(distDir, 'types')\n    if (!fs.existsSync(distTypesDir)) {\n      await mkdir(distTypesDir, { recursive: true })\n    }\n  }\n\n  setGlobal('distDir', distDir)\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const serverFields: ServerFields = {}\n\n  // Update logging state once based on next.config.js when initializing\n  consoleStore.setState({\n    logging: nextConfig.logging !== false,\n  })\n\n  const hotReloader: NextJsHotReloaderInterface = opts.turbo\n    ? await createHotReloaderTurbopack(opts, serverFields, distDir, resetFetch)\n    : new HotReloaderWebpack(opts.dir, {\n        isSrcDir: opts.isSrcDir,\n        appDir,\n        pagesDir,\n        distDir,\n        config: opts.nextConfig,\n        buildId: 'development',\n        encryptionKey: await generateEncryptionKeyBase64({\n          isBuild: false,\n          distDir,\n        }),\n        telemetry: opts.telemetry,\n        rewrites: opts.fsChecker.rewrites,\n        previewProps: opts.fsChecker.prerenderManifest.preview,\n        resetFetch,\n      })\n\n  await hotReloader.start()\n\n  // have to write this after starting hot-reloader since that\n  // cleans the dist dir\n  const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n  const routesManifest: DevRoutesManifest = {\n    version: 3,\n    caseSensitive: !!nextConfig.experimental.caseSensitiveRoutes,\n    basePath: nextConfig.basePath,\n    rewrites: opts.fsChecker.rewrites,\n    redirects: opts.fsChecker.redirects,\n    headers: opts.fsChecker.headers,\n    i18n: nextConfig.i18n || undefined,\n    skipMiddlewareUrlNormalize: nextConfig.skipMiddlewareUrlNormalize,\n  }\n  await fs.promises.writeFile(\n    routesManifestPath,\n    JSON.stringify(routesManifest)\n  )\n\n  const prerenderManifestPath = path.join(distDir, PRERENDER_MANIFEST)\n  await fs.promises.writeFile(\n    prerenderManifestPath,\n    JSON.stringify(opts.fsChecker.prerenderManifest, null, 2)\n  )\n\n  if (opts.nextConfig.experimental.nextScriptWorkers) {\n    await verifyPartytownSetup(\n      opts.dir,\n      path.join(distDir, CLIENT_STATIC_FILES_PATH)\n    )\n  }\n\n  opts.fsChecker.ensureCallback(async function ensure(item) {\n    if (item.type === 'appFile' || item.type === 'pageFile') {\n      await hotReloader.ensurePage({\n        clientOnly: false,\n        page: item.itemPath,\n        isApp: item.type === 'appFile',\n        definition: undefined,\n      })\n    }\n  })\n\n  let resolved = false\n  let prevSortedRoutes: string[] = []\n\n  await new Promise<void>(async (resolve, reject) => {\n    if (pagesDir) {\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n    }\n\n    const pages = pagesDir ? [pagesDir] : []\n    const app = appDir ? [appDir] : []\n    const directories = [...pages, ...app]\n\n    const rootDir = pagesDir || appDir\n    const files = [\n      ...getPossibleMiddlewareFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n      ...getPossibleInstrumentationHookFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n    ]\n    let nestedMiddleware: string[] = []\n\n    const envFiles = [\n      '.env.development.local',\n      '.env.local',\n      '.env.development',\n      '.env',\n    ].map((file) => path.join(dir, file))\n\n    files.push(...envFiles)\n\n    // tsconfig/jsconfig paths hot-reloading\n    const tsconfigPaths = [\n      path.join(dir, 'tsconfig.json'),\n      path.join(dir, 'jsconfig.json'),\n    ] as const\n    files.push(...tsconfigPaths)\n\n    const wp = new Watchpack({\n      ignored: (pathname: string) => {\n        return (\n          !files.some((file) => file.startsWith(pathname)) &&\n          !directories.some(\n            (d) => pathname.startsWith(d) || d.startsWith(pathname)\n          )\n        )\n      },\n    })\n    const fileWatchTimes = new Map()\n    let enabledTypeScript = usingTypeScript\n    let previousClientRouterFilters: any\n    let previousConflictingPagePaths: Set<string> = new Set()\n\n    wp.on('aggregated', async () => {\n      let middlewareMatchers: MiddlewareMatcher[] | undefined\n      const routedPages: string[] = []\n      const knownFiles = wp.getTimeInfoEntries()\n      const appPaths: Record<string, string[]> = {}\n      const pageNameSet = new Set<string>()\n      const conflictingAppPagePaths = new Set<string>()\n      const appPageFilePaths = new Map<string, string>()\n      const pagesPageFilePaths = new Map<string, string>()\n\n      let envChange = false\n      let tsconfigChange = false\n      let conflictingPageChange = 0\n      let hasRootAppNotFound = false\n\n      const { appFiles, pageFiles } = opts.fsChecker\n\n      appFiles.clear()\n      pageFiles.clear()\n      devPageFiles.clear()\n\n      const sortedKnownFiles: string[] = [...knownFiles.keys()].sort(\n        sortByPageExts(nextConfig.pageExtensions)\n      )\n\n      for (const fileName of sortedKnownFiles) {\n        if (\n          !files.includes(fileName) &&\n          !directories.some((d) => fileName.startsWith(d))\n        ) {\n          continue\n        }\n        const meta = knownFiles.get(fileName)\n\n        const watchTime = fileWatchTimes.get(fileName)\n        // If the file is showing up for the first time or the meta.timestamp is changed since last time\n        const watchTimeChange =\n          watchTime === undefined ||\n          (watchTime && watchTime !== meta?.timestamp)\n        fileWatchTimes.set(fileName, meta?.timestamp)\n\n        if (envFiles.includes(fileName)) {\n          if (watchTimeChange) {\n            envChange = true\n          }\n          continue\n        }\n\n        if (tsconfigPaths.includes(fileName)) {\n          if (fileName.endsWith('tsconfig.json')) {\n            enabledTypeScript = true\n          }\n          if (watchTimeChange) {\n            tsconfigChange = true\n          }\n          continue\n        }\n\n        if (\n          meta?.accuracy === undefined ||\n          !validFileMatcher.isPageFile(fileName)\n        ) {\n          continue\n        }\n\n        const isAppPath = Boolean(\n          appDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(appDir) + '/'\n            )\n        )\n        const isPagePath = Boolean(\n          pagesDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(pagesDir) + '/'\n            )\n        )\n\n        const rootFile = absolutePathToPage(fileName, {\n          dir: dir,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: false,\n          pagesType: PAGE_TYPES.ROOT,\n        })\n\n        if (isMiddlewareFile(rootFile)) {\n          const staticInfo = await getStaticInfoIncludingLayouts({\n            pageFilePath: fileName,\n            config: nextConfig,\n            appDir: appDir,\n            page: rootFile,\n            isDev: true,\n            isInsideAppDir: isAppPath,\n            pageExtensions: nextConfig.pageExtensions,\n          })\n          if (nextConfig.output === 'export') {\n            Log.error(\n              'Middleware cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n            continue\n          }\n          serverFields.actualMiddlewareFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualMiddlewareFile',\n            serverFields.actualMiddlewareFile\n          )\n          middlewareMatchers = staticInfo.middleware?.matchers || [\n            { regexp: '.*', originalSource: '/:path*' },\n          ]\n          continue\n        }\n        if (isInstrumentationHookFile(rootFile)) {\n          serverFields.actualInstrumentationHookFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualInstrumentationHookFile',\n            serverFields.actualInstrumentationHookFile\n          )\n          continue\n        }\n\n        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {\n          enabledTypeScript = true\n        }\n\n        if (!(isAppPath || isPagePath)) {\n          continue\n        }\n\n        // Collect all current filenames for the TS plugin to use\n        devPageFiles.add(fileName)\n\n        let pageName = absolutePathToPage(fileName, {\n          dir: isAppPath ? appDir! : pagesDir!,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: isAppPath,\n          pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n        })\n\n        if (\n          isAppPath &&\n          appDir &&\n          isMetadataRouteFile(\n            fileName.replace(appDir, ''),\n            nextConfig.pageExtensions,\n            true\n          )\n        ) {\n          const staticInfo = await getPageStaticInfo({\n            pageFilePath: fileName,\n            nextConfig: {},\n            page: pageName,\n            isDev: true,\n            pageType: PAGE_TYPES.APP,\n          })\n\n          pageName = normalizeMetadataPageToRoute(\n            pageName,\n            !!(staticInfo.generateSitemaps || staticInfo.generateImageMetadata)\n          )\n        }\n\n        if (\n          !isAppPath &&\n          pageName.startsWith('/api/') &&\n          nextConfig.output === 'export'\n        ) {\n          Log.error(\n            'API Routes cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n          )\n          continue\n        }\n\n        if (isAppPath) {\n          const isRootNotFound = validFileMatcher.isRootNotFound(fileName)\n          hasRootAppNotFound = true\n\n          if (isRootNotFound) {\n            continue\n          }\n          if (!isRootNotFound && !validFileMatcher.isAppRouterPage(fileName)) {\n            continue\n          }\n          // Ignore files/directories starting with `_` in the app directory\n          if (normalizePathSep(pageName).includes('/_')) {\n            continue\n          }\n\n          const originalPageName = pageName\n          pageName = normalizeAppPath(pageName).replace(/%5F/g, '_')\n          if (!appPaths[pageName]) {\n            appPaths[pageName] = []\n          }\n          appPaths[pageName].push(\n            opts.turbo\n              ? // Turbopack outputs the correct path which is normalized with the `_`.\n                originalPageName.replace(/%5F/g, '_')\n              : originalPageName\n          )\n\n          if (useFileSystemPublicRoutes) {\n            appFiles.add(pageName)\n          }\n\n          if (routedPages.includes(pageName)) {\n            continue\n          }\n        } else {\n          if (useFileSystemPublicRoutes) {\n            pageFiles.add(pageName)\n            // always add to nextDataRoutes for now but in future only add\n            // entries that actually use getStaticProps/getServerSideProps\n            opts.fsChecker.nextDataRoutes.add(pageName)\n          }\n        }\n        ;(isAppPath ? appPageFilePaths : pagesPageFilePaths).set(\n          pageName,\n          fileName\n        )\n\n        if (appDir && pageNameSet.has(pageName)) {\n          conflictingAppPagePaths.add(pageName)\n        } else {\n          pageNameSet.add(pageName)\n        }\n\n        /**\n         * If there is a middleware that is not declared in the root we will\n         * warn without adding it so it doesn't make its way into the system.\n         */\n        if (/[\\\\\\\\/]_middleware$/.test(pageName)) {\n          nestedMiddleware.push(pageName)\n          continue\n        }\n\n        routedPages.push(pageName)\n      }\n\n      const numConflicting = conflictingAppPagePaths.size\n      conflictingPageChange = numConflicting - previousConflictingPagePaths.size\n\n      if (conflictingPageChange !== 0) {\n        if (numConflicting > 0) {\n          let errorMessage = `Conflicting app and page file${\n            numConflicting === 1 ? ' was' : 's were'\n          } found, please remove the conflicting files to continue:\\n`\n\n          for (const p of conflictingAppPagePaths) {\n            const appPath = path.relative(dir, appPageFilePaths.get(p)!)\n            const pagesPath = path.relative(dir, pagesPageFilePaths.get(p)!)\n            errorMessage += `  \"${pagesPath}\" - \"${appPath}\"\\n`\n          }\n          hotReloader.setHmrServerError(new Error(errorMessage))\n        } else if (numConflicting === 0) {\n          hotReloader.clearHmrServerError()\n          await propagateServerField(opts, 'reloadMatchers', undefined)\n        }\n      }\n\n      previousConflictingPagePaths = conflictingAppPagePaths\n\n      let clientRouterFilters: any\n      if (nextConfig.experimental.clientRouterFilter) {\n        clientRouterFilters = createClientRouterFilter(\n          Object.keys(appPaths),\n          nextConfig.experimental.clientRouterFilterRedirects\n            ? ((nextConfig as any)._originalRedirects || []).filter(\n                (r: any) => !r.internal\n              )\n            : [],\n          nextConfig.experimental.clientRouterFilterAllowedRate\n        )\n\n        if (\n          !previousClientRouterFilters ||\n          JSON.stringify(previousClientRouterFilters) !==\n            JSON.stringify(clientRouterFilters)\n        ) {\n          envChange = true\n          previousClientRouterFilters = clientRouterFilters\n        }\n      }\n\n      if (!usingTypeScript && enabledTypeScript) {\n        // we tolerate the error here as this is best effort\n        // and the manual install command will be shown\n        await verifyTypeScript(opts)\n          .then(() => {\n            tsconfigChange = true\n          })\n          .catch(() => {})\n      }\n\n      if (envChange || tsconfigChange) {\n        if (envChange) {\n          const { loadedEnvFiles } = loadEnvConfig(\n            dir,\n            process.env.NODE_ENV === 'development',\n            Log,\n            true,\n            (envFilePath) => {\n              Log.info(`Reload env: ${envFilePath}`)\n            }\n          )\n\n          if (usingTypeScript && nextConfig.experimental?.typedEnv) {\n            // do not await, this is not essential for further process\n            createEnvDefinitions({\n              distDir,\n              loadedEnvFiles: [\n                ...loadedEnvFiles,\n                {\n                  path: nextConfig.configFileName,\n                  env: nextConfig.env,\n                  contents: '',\n                },\n              ],\n            })\n          }\n\n          await propagateServerField(opts, 'loadEnvConfig', [\n            { dev: true, forceReload: true, silent: true },\n          ])\n        }\n        let tsconfigResult:\n          | UnwrapPromise<ReturnType<typeof loadJsConfig>>\n          | undefined\n\n        if (tsconfigChange) {\n          try {\n            tsconfigResult = await loadJsConfig(dir, nextConfig)\n          } catch (_) {\n            /* do we want to log if there are syntax errors in tsconfig while editing? */\n          }\n        }\n\n        if (hotReloader.turbopackProject) {\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          const rootPath =\n            opts.nextConfig.turbopack?.root ||\n            opts.nextConfig.outputFileTracingRoot ||\n            opts.dir\n          await hotReloader.turbopackProject.update({\n            defineEnv: createDefineEnv({\n              isTurbopack: true,\n              clientRouterFilters,\n              config: nextConfig,\n              dev: true,\n              distDir,\n              fetchCacheKeyPrefix:\n                opts.nextConfig.experimental.fetchCacheKeyPrefix,\n              hasRewrites,\n              // TODO: Implement\n              middlewareMatchers: undefined,\n              projectPath: opts.dir,\n              rewrites: opts.fsChecker.rewrites,\n            }),\n            rootPath,\n            projectPath: normalizePath(path.relative(rootPath, dir)),\n          })\n        }\n\n        hotReloader.activeWebpackConfigs?.forEach((config, idx) => {\n          const isClient = idx === 0\n          const isNodeServer = idx === 1\n          const isEdgeServer = idx === 2\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          if (tsconfigChange) {\n            config.resolve?.plugins?.forEach((plugin: any) => {\n              // look for the JsConfigPathsPlugin and update with\n              // the latest paths/baseUrl config\n              if (plugin instanceof JsConfigPathsPlugin && tsconfigResult) {\n                const { resolvedBaseUrl, jsConfig } = tsconfigResult\n                const currentResolvedBaseUrl = plugin.resolvedBaseUrl\n                const resolvedUrlIndex = config.resolve?.modules?.findIndex(\n                  (item) => item === currentResolvedBaseUrl?.baseUrl\n                )\n\n                if (resolvedBaseUrl) {\n                  if (\n                    resolvedBaseUrl.baseUrl !== currentResolvedBaseUrl?.baseUrl\n                  ) {\n                    // remove old baseUrl and add new one\n                    if (resolvedUrlIndex && resolvedUrlIndex > -1) {\n                      config.resolve?.modules?.splice(resolvedUrlIndex, 1)\n                    }\n\n                    // If the resolvedBaseUrl is implicit we only remove the previous value.\n                    // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n                    if (!resolvedBaseUrl.isImplicit) {\n                      config.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n                    }\n                  }\n                }\n\n                if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n                  Object.keys(plugin.paths).forEach((key) => {\n                    delete plugin.paths[key]\n                  })\n                  Object.assign(plugin.paths, jsConfig.compilerOptions.paths)\n                  plugin.resolvedBaseUrl = resolvedBaseUrl\n                }\n              }\n            })\n          }\n\n          if (envChange) {\n            config.plugins?.forEach((plugin: any) => {\n              // we look for the DefinePlugin definitions so we can\n              // update them on the active compilers\n              if (\n                plugin &&\n                typeof plugin.definitions === 'object' &&\n                plugin.definitions.__NEXT_DEFINE_ENV\n              ) {\n                const newDefine = getDefineEnv({\n                  isTurbopack: false,\n                  clientRouterFilters,\n                  config: nextConfig,\n                  dev: true,\n                  distDir,\n                  fetchCacheKeyPrefix:\n                    opts.nextConfig.experimental.fetchCacheKeyPrefix,\n                  hasRewrites,\n                  isClient,\n                  isEdgeServer,\n                  isNodeServer,\n                  middlewareMatchers: undefined,\n                  projectPath: opts.dir,\n                  rewrites: opts.fsChecker.rewrites,\n                })\n\n                Object.keys(plugin.definitions).forEach((key) => {\n                  if (!(key in newDefine)) {\n                    delete plugin.definitions[key]\n                  }\n                })\n                Object.assign(plugin.definitions, newDefine)\n              }\n            })\n          }\n        })\n        await hotReloader.invalidate({\n          reloadAfterInvalidation: envChange,\n        })\n      }\n\n      if (nestedMiddleware.length > 0) {\n        Log.error(\n          new NestedMiddlewareError(\n            nestedMiddleware,\n            dir,\n            (pagesDir || appDir)!\n          ).message\n        )\n        nestedMiddleware = []\n      }\n\n      // Make sure to sort parallel routes to make the result deterministic.\n      serverFields.appPathRoutes = Object.fromEntries(\n        Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n      )\n      await propagateServerField(\n        opts,\n        'appPathRoutes',\n        serverFields.appPathRoutes\n      )\n\n      // TODO: pass this to fsChecker/next-dev-server?\n      serverFields.middleware = middlewareMatchers\n        ? {\n            match: null as any,\n            page: '/',\n            matchers: middlewareMatchers,\n          }\n        : undefined\n\n      await propagateServerField(opts, 'middleware', serverFields.middleware)\n      serverFields.hasAppNotFound = hasRootAppNotFound\n\n      opts.fsChecker.middlewareMatcher = serverFields.middleware?.matchers\n        ? getMiddlewareRouteMatcher(serverFields.middleware?.matchers)\n        : undefined\n\n      const interceptionRoutes = generateInterceptionRoutesRewrites(\n        Object.keys(appPaths),\n        opts.nextConfig.basePath\n      ).map((item) =>\n        buildCustomRoute(\n          'before_files_rewrite',\n          item,\n          opts.nextConfig.basePath,\n          opts.nextConfig.experimental.caseSensitiveRoutes\n        )\n      )\n\n      opts.fsChecker.rewrites.beforeFiles.push(...interceptionRoutes)\n\n      const exportPathMap =\n        (typeof nextConfig.exportPathMap === 'function' &&\n          (await nextConfig.exportPathMap?.(\n            {},\n            {\n              dev: true,\n              dir: opts.dir,\n              outDir: null,\n              distDir: distDir,\n              buildId: 'development',\n            }\n          ))) ||\n        {}\n\n      const exportPathMapEntries = Object.entries(exportPathMap || {})\n\n      if (exportPathMapEntries.length > 0) {\n        opts.fsChecker.exportPathMapRoutes = exportPathMapEntries.map(\n          ([key, value]) =>\n            buildCustomRoute(\n              'before_files_rewrite',\n              {\n                source: key,\n                destination: `${value.page}${\n                  value.query ? '?' : ''\n                }${qs.stringify(value.query)}`,\n              },\n              opts.nextConfig.basePath,\n              opts.nextConfig.experimental.caseSensitiveRoutes\n            )\n        )\n      }\n\n      try {\n        // we serve a separate manifest with all pages for the client in\n        // dev mode so that we can match a page after a rewrite on the client\n        // before it has been built and is populated in the _buildManifest\n        const sortedRoutes = getSortedRoutes(routedPages)\n\n        opts.fsChecker.dynamicRoutes = sortedRoutes.map(\n          (page): FilesystemDynamicRoute => {\n            const regex = getRouteRegex(page)\n            return {\n              regex: regex.re.toString(),\n              match: getRouteMatcher(regex),\n              page,\n            }\n          }\n        )\n\n        const dataRoutes: typeof opts.fsChecker.dynamicRoutes = []\n\n        for (const page of sortedRoutes) {\n          const route = buildDataRoute(page, 'development')\n          const routeRegex = getRouteRegex(route.page)\n          dataRoutes.push({\n            ...route,\n            regex: routeRegex.re.toString(),\n            match: getRouteMatcher({\n              // TODO: fix this in the manifest itself, must also be fixed in\n              // upstream builder that relies on this\n              re: opts.nextConfig.i18n\n                ? new RegExp(\n                    route.dataRouteRegex.replace(\n                      `/development/`,\n                      `/development/(?<nextLocale>[^/]+?)/`\n                    )\n                  )\n                : new RegExp(route.dataRouteRegex),\n              groups: routeRegex.groups,\n            }),\n          })\n        }\n        opts.fsChecker.dynamicRoutes.unshift(...dataRoutes)\n\n        if (!prevSortedRoutes?.every((val, idx) => val === sortedRoutes[idx])) {\n          const addedRoutes = sortedRoutes.filter(\n            (route) => !prevSortedRoutes.includes(route)\n          )\n          const removedRoutes = prevSortedRoutes.filter(\n            (route) => !sortedRoutes.includes(route)\n          )\n\n          // emit the change so clients fetch the update\n          hotReloader.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE,\n            data: [\n              {\n                devPagesManifest: true,\n              },\n            ],\n          })\n\n          addedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n              data: [route],\n            })\n          })\n\n          removedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n              data: [route],\n            })\n          })\n        }\n        prevSortedRoutes = sortedRoutes\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      } catch (e) {\n        if (!resolved) {\n          reject(e)\n          resolved = true\n        } else {\n          Log.warn('Failed to reload dynamic routes:', e)\n        }\n      } finally {\n        // Reload the matchers. The filesystem would have been written to,\n        // and the matchers need to re-scan it to update the router.\n        await propagateServerField(opts, 'reloadMatchers', undefined)\n      }\n    })\n\n    wp.watch({ directories: [dir], startTime: 0 })\n  })\n\n  const clientPagesManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_PAGES_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(clientPagesManifestPath)\n\n  const devMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devMiddlewareManifestPath)\n\n  const devTurbopackMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devTurbopackMiddlewareManifestPath)\n\n  async function requestHandler(req: IncomingMessage, res: ServerResponse) {\n    const parsedUrl = url.parse(req.url || '/')\n\n    if (parsedUrl.pathname?.includes(clientPagesManifestPath)) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(\n        JSON.stringify({\n          pages: prevSortedRoutes.filter(\n            (route) => !opts.fsChecker.appFiles.has(route)\n          ),\n        })\n      )\n      return { finished: true }\n    }\n\n    if (\n      parsedUrl.pathname?.includes(devMiddlewareManifestPath) ||\n      parsedUrl.pathname?.includes(devTurbopackMiddlewareManifestPath)\n    ) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(JSON.stringify(serverFields.middleware?.matchers || []))\n      return { finished: true }\n    }\n    return { finished: false }\n  }\n\n  function logErrorWithOriginalStack(\n    err: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ) {\n    if (err instanceof ModuleBuildError) {\n      // Errors that may come from issues from the user's code\n      Log.error(err.message)\n    } else if (err instanceof TurbopackInternalError) {\n      // An internal Turbopack error that has been handled by next-swc, written\n      // to disk and a simplified message shown to user on the Rust side.\n    } else if (type === 'warning') {\n      Log.warn(err)\n    } else if (type === 'app-dir') {\n      Log.error(err)\n    } else if (type) {\n      Log.error(`${type}:`, err)\n    } else {\n      Log.error(err)\n    }\n  }\n\n  return {\n    serverFields,\n    hotReloader,\n    requestHandler,\n    logErrorWithOriginalStack,\n\n    async ensureMiddleware(requestUrl?: string) {\n      if (!serverFields.actualMiddlewareFile) return\n      return hotReloader.ensurePage({\n        page: serverFields.actualMiddlewareFile,\n        clientOnly: false,\n        definition: undefined,\n        url: requestUrl,\n      })\n    },\n  }\n}\n\nexport async function setupDevBundler(opts: SetupOpts) {\n  const isSrcDir = path\n    .relative(opts.dir, opts.pagesDir || opts.appDir || '')\n    .startsWith('src')\n\n  const result = await startWatcher({\n    ...opts,\n    isSrcDir,\n  })\n\n  opts.telemetry.record(\n    eventCliSession(\n      path.join(opts.dir, opts.nextConfig.distDir),\n      opts.nextConfig,\n      {\n        webpackVersion: 5,\n        isSrcDir,\n        turboFlag: !!opts.turbo,\n        cliCommand: 'dev',\n        appDir: !!opts.appDir,\n        pagesDir: !!opts.pagesDir,\n        isCustomServer: !!opts.isCustomServer,\n        hasNowJson: !!(await findUp('now.json', { cwd: opts.dir })),\n      }\n    )\n  )\n\n  // Track build features for dev server here:\n  opts.telemetry.record({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName: 'turbopackPersistentCaching',\n      invocationCount: isPersistentCachingEnabled(opts.nextConfig) ? 1 : 0,\n    },\n  })\n\n  return result\n}\n\nexport type DevBundler = Awaited<ReturnType<typeof setupDevBundler>>\n\n// Returns a trace rewritten through Turbopack's sourcemaps\n"], "names": ["getPageStaticInfo", "createDefineEnv", "fs", "mkdir", "url", "path", "qs", "Watchpack", "loadEnvConfig", "findUp", "buildCustomRoute", "Log", "HotReloaderWebpack", "setGlobal", "loadJsConfig", "createValidFileMatcher", "EVENT_BUILD_FEATURE_USAGE", "eventCliSession", "getSortedRoutes", "getStaticInfoIncludingLayouts", "sortByPageExts", "verifyTypeScriptSetup", "verifyPartytownSetup", "getRouteRegex", "normalizeAppPath", "buildDataRoute", "getRouteMatcher", "normalizePathSep", "createClientRouterFilter", "absolutePathToPage", "generateInterceptionRoutesRewrites", "CLIENT_STATIC_FILES_PATH", "DEV_CLIENT_PAGES_MANIFEST", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "ROUTES_MANIFEST", "PRERENDER_MANIFEST", "getMiddlewareRouteMatcher", "isMiddlewareFile", "NestedMiddlewareError", "isInstrumentationHookFile", "getPossibleMiddlewareFilenames", "getPossibleInstrumentationHookFilenames", "devPageFiles", "HMR_ACTIONS_SENT_TO_BROWSER", "PAGE_TYPES", "createHotReloaderTurbopack", "generateEncryptionKeyBase64", "isMetadataRouteFile", "normalizeMetadataPageToRoute", "createEnvDefinitions", "JsConfigPathsPlugin", "store", "consoleStore", "isPersistentCachingEnabled", "ModuleBuildError", "TurbopackInternalError", "getDefineEnv", "normalizePath", "verifyTypeScript", "opts", "usingTypeScript", "verifyResult", "dir", "distDir", "nextConfig", "intentDirs", "pagesDir", "appDir", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "version", "propagateServerField", "field", "args", "renderServer", "instance", "startWatcher", "resetFetch", "useFileSystemPublicRoutes", "join", "distTypesDir", "existsSync", "recursive", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "serverFields", "setState", "logging", "hotReloader", "turbo", "isSrcDir", "config", "buildId", "<PERSON><PERSON><PERSON>", "isBuild", "telemetry", "rewrites", "fs<PERSON><PERSON><PERSON>", "previewProps", "prerenderManifest", "preview", "start", "routesManifestPath", "routesManifest", "caseSensitive", "experimental", "caseSensitiveRoutes", "basePath", "redirects", "headers", "i18n", "undefined", "skipMiddlewareUrlNormalize", "promises", "writeFile", "JSON", "stringify", "prerenderManifestPath", "nextScriptWorkers", "ensure<PERSON><PERSON>back", "ensure", "item", "type", "ensurePage", "clientOnly", "page", "itemPath", "isApp", "definition", "resolved", "prevSortedRoutes", "Promise", "resolve", "reject", "readdir", "_", "files", "length", "pages", "app", "directories", "rootDir", "nestedMiddleware", "envFiles", "map", "file", "push", "tsconfigPaths", "wp", "ignored", "pathname", "some", "startsWith", "d", "fileWatchTimes", "Map", "enabledTypeScript", "previousClientRouterFilters", "previousConflictingPagePaths", "Set", "on", "middlewareMatchers", "routedPages", "knownFiles", "getTimeInfoEntries", "appPaths", "pageNameSet", "conflictingAppPagePaths", "appPageFilePaths", "pagesPageFilePaths", "envChange", "tsconfigChange", "conflictingPageChange", "hasRootAppNotFound", "appFiles", "pageFiles", "clear", "sortedKnownFiles", "keys", "sort", "fileName", "includes", "meta", "get", "watchTime", "watchTimeChange", "timestamp", "set", "endsWith", "accuracy", "isPageFile", "isAppPath", "isPagePath", "rootFile", "extensions", "keepIndex", "pagesType", "ROOT", "staticInfo", "pageFilePath", "isDev", "isInsideAppDir", "output", "error", "actualMiddlewareFile", "middleware", "matchers", "regexp", "originalSource", "actualInstrumentationHookFile", "add", "pageName", "APP", "PAGES", "replace", "pageType", "generateSitemaps", "generateImageMetadata", "isRootNotFound", "isAppRouterPage", "originalPageName", "nextDataRoutes", "has", "test", "numConflicting", "size", "errorMessage", "p", "appPath", "relative", "pagesPath", "setHmrServerError", "Error", "clearHmrServerError", "clientRouterFilters", "clientRouterFilter", "Object", "clientRouterFilterRedirects", "_originalRedirects", "r", "internal", "clientRouterFilterAllowedRate", "then", "catch", "loadedEnvFiles", "process", "env", "NODE_ENV", "env<PERSON><PERSON><PERSON><PERSON>", "info", "typedEnv", "configFileName", "contents", "dev", "forceReload", "silent", "tsconfigResult", "turbopackProject", "hasRewrites", "afterFiles", "beforeFiles", "fallback", "rootPath", "turbopack", "root", "outputFileTracingRoot", "update", "defineEnv", "isTurbopack", "fetchCacheKeyPrefix", "projectPath", "activeWebpackConfigs", "for<PERSON>ach", "idx", "isClient", "isNodeServer", "isEdgeServer", "plugins", "plugin", "jsConfig", "resolvedBaseUrl", "currentResolvedBaseUrl", "resolvedUrlIndex", "modules", "findIndex", "baseUrl", "splice", "isImplicit", "compilerOptions", "paths", "key", "assign", "definitions", "__NEXT_DEFINE_ENV", "newDefine", "invalidate", "reloadAfterInvalidation", "message", "appPathRoutes", "fromEntries", "entries", "k", "v", "match", "hasAppNotFound", "middlewareMatcher", "interceptionRoutes", "exportPathMap", "outDir", "exportPathMapEntries", "exportPathMapRoutes", "value", "source", "destination", "query", "sortedRoutes", "dynamicRoutes", "regex", "re", "toString", "dataRoutes", "route", "routeRegex", "RegExp", "dataRouteRegex", "groups", "unshift", "every", "val", "addedRoutes", "removedRoutes", "send", "action", "DEV_PAGES_MANIFEST_UPDATE", "data", "devPagesManifest", "ADDED_PAGE", "REMOVED_PAGE", "e", "warn", "watch", "startTime", "clientPagesManifestPath", "devVirtualFsItems", "devMiddlewareManifestPath", "devTurbopackMiddlewareManifestPath", "requestHandler", "req", "res", "parsedUrl", "parse", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "end", "finished", "logErrorWithOriginalStack", "err", "ensureMiddleware", "requestUrl", "setupDevBundler", "result", "record", "webpackVersion", "turboFlag", "cliCommand", "isCustomServer", "has<PERSON>ow<PERSON><PERSON>", "cwd", "eventName", "payload", "featureName", "invocationCount"], "mappings": "AAGA,SACEA,iBAAiB,QAEZ,+CAA8C;AAMrD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,OAAOC,QAAQ,KAAI;AACnB,SAASC,KAAK,QAAQ,cAAa;AACnC,OAAOC,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,OAAOC,eAAe,+BAA8B;AACpD,SAASC,aAAa,QAAQ,YAAW;AACzC,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,YAAYC,SAAS,4BAA2B;AAChD,OAAOC,wBAAwB,iCAAgC;AAC/D,SAASC,SAAS,QAAQ,wBAAuB;AAGjD,OAAOC,kBAAkB,+BAA8B;AACvD,SAASC,sBAAsB,QAAQ,oBAAmB;AAC1D,SACEC,yBAAyB,EACzBC,eAAe,QACV,4BAA2B;AAClC,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SACEC,6BAA6B,EAC7BC,cAAc,QACT,yBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,sCAAqC;AAC1E,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,eAAe,QAAQ,iDAAgD;AAChF,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,wBAAwB,QAAQ,2CAA0C;AACnF,SAASC,kBAAkB,QAAQ,sDAAqD;AACxF,SAASC,kCAAkC,QAAQ,qDAAoD;AAEvG,SACEC,wBAAwB,EACxBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,wBAAwB,EACxBC,oCAAoC,EACpCC,eAAe,EACfC,kBAAkB,QACb,gCAA+B;AAEtC,SAASC,yBAAyB,QAAQ,4DAA2D;AAErG,SACEC,gBAAgB,EAChBC,qBAAqB,EACrBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,uCAAuC,QAClC,uBAAsB;AAC7B,SAASC,YAAY,QAAQ,0DAAyD;AAEtF,SAASC,2BAA2B,QAAQ,+BAA8B;AAC1E,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,2BAA2B,QAAQ,2CAA0C;AACtF,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,4BAA4B,QAAQ,2CAA0C;AACvF,SAASC,oBAAoB,QAAQ,yCAAwC;AAC7E,SAASC,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,SAASC,YAAY,QAAQ,8BAA6B;AACnE,SACEC,0BAA0B,EAC1BC,gBAAgB,EAChBC,sBAAsB,QACjB,sCAAqC;AAC5C,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,8BAA6B;AAiD3D,eAAeC,iBAAiBC,IAAe;IAC7C,IAAIC,kBAAkB;IACtB,MAAMC,eAAe,MAAM1C,sBAAsB;QAC/C2C,KAAKH,KAAKG,GAAG;QACbC,SAASJ,KAAKK,UAAU,CAACD,OAAO;QAChCE,YAAY;YAACN,KAAKO,QAAQ;YAAEP,KAAKQ,MAAM;SAAC,CAACC,MAAM,CAACC;QAChDC,oBAAoB;QACpBC,cAAcZ,KAAKK,UAAU,CAACQ,UAAU,CAACD,YAAY;QACrDE,qBAAqBd,KAAKK,UAAU,CAACU,MAAM,CAACD,mBAAmB;QAC/DE,WAAW,CAAC,CAAChB,KAAKQ,MAAM;QACxBS,aAAa,CAAC,CAACjB,KAAKO,QAAQ;IAC9B;IAEA,IAAIL,aAAagB,OAAO,EAAE;QACxBjB,kBAAkB;IACpB;IACA,OAAOA;AACT;AAEA,OAAO,eAAekB,qBACpBnB,IAAe,EACfoB,KAA8B,EAC9BC,IAAS;QAEHrB,6BAAAA;IAAN,QAAMA,qBAAAA,KAAKsB,YAAY,sBAAjBtB,8BAAAA,mBAAmBuB,QAAQ,qBAA3BvB,4BAA6BmB,oBAAoB,CAACnB,KAAKG,GAAG,EAAEiB,OAAOC;AAC3E;AAEA,eAAeG,aACbxB,IAEC;IAED,MAAM,EAAEK,UAAU,EAAEG,MAAM,EAAED,QAAQ,EAAEJ,GAAG,EAAEsB,UAAU,EAAE,GAAGzB;IAC1D,MAAM,EAAE0B,yBAAyB,EAAE,GAAGrB;IACtC,MAAMJ,kBAAkB,MAAMF,iBAAiBC;IAE/C,MAAMI,UAAU5D,KAAKmF,IAAI,CAAC3B,KAAKG,GAAG,EAAEH,KAAKK,UAAU,CAACD,OAAO;IAE3D,4CAA4C;IAC5C,IAAIH,iBAAiB;QACnB,MAAM2B,eAAepF,KAAKmF,IAAI,CAACvB,SAAS;QACxC,IAAI,CAAC/D,GAAGwF,UAAU,CAACD,eAAe;YAChC,MAAMtF,MAAMsF,cAAc;gBAAEE,WAAW;YAAK;QAC9C;IACF;IAEA9E,UAAU,WAAWoD;IACrBpD,UAAU,SAASqB;IAEnB,MAAM0D,mBAAmB7E,uBACvBmD,WAAW2B,cAAc,EACzBxB;IAGF,MAAMyB,eAA6B,CAAC;IAEpC,sEAAsE;IACtExC,aAAayC,QAAQ,CAAC;QACpBC,SAAS9B,WAAW8B,OAAO,KAAK;IAClC;IAEA,MAAMC,cAA0CpC,KAAKqC,KAAK,GACtD,MAAMnD,2BAA2Bc,MAAMiC,cAAc7B,SAASqB,cAC9D,IAAI1E,mBAAmBiD,KAAKG,GAAG,EAAE;QAC/BmC,UAAUtC,KAAKsC,QAAQ;QACvB9B;QACAD;QACAH;QACAmC,QAAQvC,KAAKK,UAAU;QACvBmC,SAAS;QACTC,eAAe,MAAMtD,4BAA4B;YAC/CuD,SAAS;YACTtC;QACF;QACAuC,WAAW3C,KAAK2C,SAAS;QACzBC,UAAU5C,KAAK6C,SAAS,CAACD,QAAQ;QACjCE,cAAc9C,KAAK6C,SAAS,CAACE,iBAAiB,CAACC,OAAO;QACtDvB;IACF;IAEJ,MAAMW,YAAYa,KAAK;IAEvB,4DAA4D;IAC5D,sBAAsB;IACtB,MAAMC,qBAAqB1G,KAAKmF,IAAI,CAACvB,SAAS7B;IAC9C,MAAM4E,iBAAoC;QACxCjC,SAAS;QACTkC,eAAe,CAAC,CAAC/C,WAAWgD,YAAY,CAACC,mBAAmB;QAC5DC,UAAUlD,WAAWkD,QAAQ;QAC7BX,UAAU5C,KAAK6C,SAAS,CAACD,QAAQ;QACjCY,WAAWxD,KAAK6C,SAAS,CAACW,SAAS;QACnCC,SAASzD,KAAK6C,SAAS,CAACY,OAAO;QAC/BC,MAAMrD,WAAWqD,IAAI,IAAIC;QACzBC,4BAA4BvD,WAAWuD,0BAA0B;IACnE;IACA,MAAMvH,GAAGwH,QAAQ,CAACC,SAAS,CACzBZ,oBACAa,KAAKC,SAAS,CAACb;IAGjB,MAAMc,wBAAwBzH,KAAKmF,IAAI,CAACvB,SAAS5B;IACjD,MAAMnC,GAAGwH,QAAQ,CAACC,SAAS,CACzBG,uBACAF,KAAKC,SAAS,CAAChE,KAAK6C,SAAS,CAACE,iBAAiB,EAAE,MAAM;IAGzD,IAAI/C,KAAKK,UAAU,CAACgD,YAAY,CAACa,iBAAiB,EAAE;QAClD,MAAMzG,qBACJuC,KAAKG,GAAG,EACR3D,KAAKmF,IAAI,CAACvB,SAASlC;IAEvB;IAEA8B,KAAK6C,SAAS,CAACsB,cAAc,CAAC,eAAeC,OAAOC,IAAI;QACtD,IAAIA,KAAKC,IAAI,KAAK,aAAaD,KAAKC,IAAI,KAAK,YAAY;YACvD,MAAMlC,YAAYmC,UAAU,CAAC;gBAC3BC,YAAY;gBACZC,MAAMJ,KAAKK,QAAQ;gBACnBC,OAAON,KAAKC,IAAI,KAAK;gBACrBM,YAAYjB;YACd;QACF;IACF;IAEA,IAAIkB,WAAW;IACf,IAAIC,mBAA6B,EAAE;IAEnC,MAAM,IAAIC,QAAc,OAAOC,SAASC;QACtC,IAAI1E,UAAU;YACZ,yDAAyD;YACzDlE,GAAG6I,OAAO,CAAC3E,UAAU,CAAC4E,GAAGC;gBACvB,IAAIA,yBAAAA,MAAOC,MAAM,EAAE;oBACjB;gBACF;gBAEA,IAAI,CAACR,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF;QACF;QAEA,MAAMS,QAAQ/E,WAAW;YAACA;SAAS,GAAG,EAAE;QACxC,MAAMgF,MAAM/E,SAAS;YAACA;SAAO,GAAG,EAAE;QAClC,MAAMgF,cAAc;eAAIF;eAAUC;SAAI;QAEtC,MAAME,UAAUlF,YAAYC;QAC5B,MAAM4E,QAAQ;eACTvG,+BACDrC,KAAKmF,IAAI,CAAC8D,SAAU,OACpBpF,WAAW2B,cAAc;eAExBlD,wCACDtC,KAAKmF,IAAI,CAAC8D,SAAU,OACpBpF,WAAW2B,cAAc;SAE5B;QACD,IAAI0D,mBAA6B,EAAE;QAEnC,MAAMC,WAAW;YACf;YACA;YACA;YACA;SACD,CAACC,GAAG,CAAC,CAACC,OAASrJ,KAAKmF,IAAI,CAACxB,KAAK0F;QAE/BT,MAAMU,IAAI,IAAIH;QAEd,wCAAwC;QACxC,MAAMI,gBAAgB;YACpBvJ,KAAKmF,IAAI,CAACxB,KAAK;YACf3D,KAAKmF,IAAI,CAACxB,KAAK;SAChB;QACDiF,MAAMU,IAAI,IAAIC;QAEd,MAAMC,KAAK,IAAItJ,UAAU;YACvBuJ,SAAS,CAACC;gBACR,OACE,CAACd,MAAMe,IAAI,CAAC,CAACN,OAASA,KAAKO,UAAU,CAACF,cACtC,CAACV,YAAYW,IAAI,CACf,CAACE,IAAMH,SAASE,UAAU,CAACC,MAAMA,EAAED,UAAU,CAACF;YAGpD;QACF;QACA,MAAMI,iBAAiB,IAAIC;QAC3B,IAAIC,oBAAoBvG;QACxB,IAAIwG;QACJ,IAAIC,+BAA4C,IAAIC;QAEpDX,GAAGY,EAAE,CAAC,cAAc;gBAyeiB3E,0BACLA;YAze9B,IAAI4E;YACJ,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAaf,GAAGgB,kBAAkB;YACxC,MAAMC,WAAqC,CAAC;YAC5C,MAAMC,cAAc,IAAIP;YACxB,MAAMQ,0BAA0B,IAAIR;YACpC,MAAMS,mBAAmB,IAAIb;YAC7B,MAAMc,qBAAqB,IAAId;YAE/B,IAAIe,YAAY;YAChB,IAAIC,iBAAiB;YACrB,IAAIC,wBAAwB;YAC5B,IAAIC,qBAAqB;YAEzB,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAG3H,KAAK6C,SAAS;YAE9C6E,SAASE,KAAK;YACdD,UAAUC,KAAK;YACf7I,aAAa6I,KAAK;YAElB,MAAMC,mBAA6B;mBAAId,WAAWe,IAAI;aAAG,CAACC,IAAI,CAC5DxK,eAAe8C,WAAW2B,cAAc;YAG1C,KAAK,MAAMgG,YAAYH,iBAAkB;gBACvC,IACE,CAACzC,MAAM6C,QAAQ,CAACD,aAChB,CAACxC,YAAYW,IAAI,CAAC,CAACE,IAAM2B,SAAS5B,UAAU,CAACC,KAC7C;oBACA;gBACF;gBACA,MAAM6B,OAAOnB,WAAWoB,GAAG,CAACH;gBAE5B,MAAMI,YAAY9B,eAAe6B,GAAG,CAACH;gBACrC,gGAAgG;gBAChG,MAAMK,kBACJD,cAAczE,aACbyE,aAAaA,eAAcF,wBAAAA,KAAMI,SAAS;gBAC7ChC,eAAeiC,GAAG,CAACP,UAAUE,wBAAAA,KAAMI,SAAS;gBAE5C,IAAI3C,SAASsC,QAAQ,CAACD,WAAW;oBAC/B,IAAIK,iBAAiB;wBACnBf,YAAY;oBACd;oBACA;gBACF;gBAEA,IAAIvB,cAAckC,QAAQ,CAACD,WAAW;oBACpC,IAAIA,SAASQ,QAAQ,CAAC,kBAAkB;wBACtChC,oBAAoB;oBACtB;oBACA,IAAI6B,iBAAiB;wBACnBd,iBAAiB;oBACnB;oBACA;gBACF;gBAEA,IACEW,CAAAA,wBAAAA,KAAMO,QAAQ,MAAK9E,aACnB,CAAC5B,iBAAiB2G,UAAU,CAACV,WAC7B;oBACA;gBACF;gBAEA,MAAMW,YAAYjI,QAChBF,UACE1C,iBAAiBkK,UAAU5B,UAAU,CACnCtI,iBAAiB0C,UAAU;gBAGjC,MAAMoI,aAAalI,QACjBH,YACEzC,iBAAiBkK,UAAU5B,UAAU,CACnCtI,iBAAiByC,YAAY;gBAInC,MAAMsI,WAAW7K,mBAAmBgK,UAAU;oBAC5C7H,KAAKA;oBACL2I,YAAYzI,WAAW2B,cAAc;oBACrC+G,WAAW;oBACXC,WAAW/J,WAAWgK,IAAI;gBAC5B;gBAEA,IAAIvK,iBAAiBmK,WAAW;wBAsBTK;oBArBrB,MAAMA,aAAa,MAAM5L,8BAA8B;wBACrD6L,cAAcnB;wBACdzF,QAAQlC;wBACRG,QAAQA;wBACRiE,MAAMoE;wBACNO,OAAO;wBACPC,gBAAgBV;wBAChB3G,gBAAgB3B,WAAW2B,cAAc;oBAC3C;oBACA,IAAI3B,WAAWiJ,MAAM,KAAK,UAAU;wBAClCxM,IAAIyM,KAAK,CACP;wBAEF;oBACF;oBACAtH,aAAauH,oBAAoB,GAAGX;oBACpC,MAAM1H,qBACJnB,MACA,wBACAiC,aAAauH,oBAAoB;oBAEnC3C,qBAAqBqC,EAAAA,yBAAAA,WAAWO,UAAU,qBAArBP,uBAAuBQ,QAAQ,KAAI;wBACtD;4BAAEC,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBACD;gBACF;gBACA,IAAIhL,0BAA0BiK,WAAW;oBACvC5G,aAAa4H,6BAA6B,GAAGhB;oBAC7C,MAAM1H,qBACJnB,MACA,iCACAiC,aAAa4H,6BAA6B;oBAE5C;gBACF;gBAEA,IAAI7B,SAASQ,QAAQ,CAAC,UAAUR,SAASQ,QAAQ,CAAC,SAAS;oBACzDhC,oBAAoB;gBACtB;gBAEA,IAAI,CAAEmC,CAAAA,aAAaC,UAAS,GAAI;oBAC9B;gBACF;gBAEA,yDAAyD;gBACzD7J,aAAa+K,GAAG,CAAC9B;gBAEjB,IAAI+B,WAAW/L,mBAAmBgK,UAAU;oBAC1C7H,KAAKwI,YAAYnI,SAAUD;oBAC3BuI,YAAYzI,WAAW2B,cAAc;oBACrC+G,WAAWJ;oBACXK,WAAWL,YAAY1J,WAAW+K,GAAG,GAAG/K,WAAWgL,KAAK;gBAC1D;gBAEA,IACEtB,aACAnI,UACApB,oBACE4I,SAASkC,OAAO,CAAC1J,QAAQ,KACzBH,WAAW2B,cAAc,EACzB,OAEF;oBACA,MAAMkH,aAAa,MAAM/M,kBAAkB;wBACzCgN,cAAcnB;wBACd3H,YAAY,CAAC;wBACboE,MAAMsF;wBACNX,OAAO;wBACPe,UAAUlL,WAAW+K,GAAG;oBAC1B;oBAEAD,WAAW1K,6BACT0K,UACA,CAAC,CAAEb,CAAAA,WAAWkB,gBAAgB,IAAIlB,WAAWmB,qBAAqB,AAAD;gBAErE;gBAEA,IACE,CAAC1B,aACDoB,SAAS3D,UAAU,CAAC,YACpB/F,WAAWiJ,MAAM,KAAK,UACtB;oBACAxM,IAAIyM,KAAK,CACP;oBAEF;gBACF;gBAEA,IAAIZ,WAAW;oBACb,MAAM2B,iBAAiBvI,iBAAiBuI,cAAc,CAACtC;oBACvDP,qBAAqB;oBAErB,IAAI6C,gBAAgB;wBAClB;oBACF;oBACA,IAAI,CAACA,kBAAkB,CAACvI,iBAAiBwI,eAAe,CAACvC,WAAW;wBAClE;oBACF;oBACA,kEAAkE;oBAClE,IAAIlK,iBAAiBiM,UAAU9B,QAAQ,CAAC,OAAO;wBAC7C;oBACF;oBAEA,MAAMuC,mBAAmBT;oBACzBA,WAAWpM,iBAAiBoM,UAAUG,OAAO,CAAC,QAAQ;oBACtD,IAAI,CAACjD,QAAQ,CAAC8C,SAAS,EAAE;wBACvB9C,QAAQ,CAAC8C,SAAS,GAAG,EAAE;oBACzB;oBACA9C,QAAQ,CAAC8C,SAAS,CAACjE,IAAI,CACrB9F,KAAKqC,KAAK,GAENmI,iBAAiBN,OAAO,CAAC,QAAQ,OACjCM;oBAGN,IAAI9I,2BAA2B;wBAC7BgG,SAASoC,GAAG,CAACC;oBACf;oBAEA,IAAIjD,YAAYmB,QAAQ,CAAC8B,WAAW;wBAClC;oBACF;gBACF,OAAO;oBACL,IAAIrI,2BAA2B;wBAC7BiG,UAAUmC,GAAG,CAACC;wBACd,8DAA8D;wBAC9D,8DAA8D;wBAC9D/J,KAAK6C,SAAS,CAAC4H,cAAc,CAACX,GAAG,CAACC;oBACpC;gBACF;;gBACEpB,CAAAA,YAAYvB,mBAAmBC,kBAAiB,EAAGkB,GAAG,CACtDwB,UACA/B;gBAGF,IAAIxH,UAAU0G,YAAYwD,GAAG,CAACX,WAAW;oBACvC5C,wBAAwB2C,GAAG,CAACC;gBAC9B,OAAO;oBACL7C,YAAY4C,GAAG,CAACC;gBAClB;gBAEA;;;SAGC,GACD,IAAI,sBAAsBY,IAAI,CAACZ,WAAW;oBACxCrE,iBAAiBI,IAAI,CAACiE;oBACtB;gBACF;gBAEAjD,YAAYhB,IAAI,CAACiE;YACnB;YAEA,MAAMa,iBAAiBzD,wBAAwB0D,IAAI;YACnDrD,wBAAwBoD,iBAAiBlE,6BAA6BmE,IAAI;YAE1E,IAAIrD,0BAA0B,GAAG;gBAC/B,IAAIoD,iBAAiB,GAAG;oBACtB,IAAIE,eAAe,CAAC,6BAA6B,EAC/CF,mBAAmB,IAAI,SAAS,SACjC,0DAA0D,CAAC;oBAE5D,KAAK,MAAMG,KAAK5D,wBAAyB;wBACvC,MAAM6D,UAAUxO,KAAKyO,QAAQ,CAAC9K,KAAKiH,iBAAiBe,GAAG,CAAC4C;wBACxD,MAAMG,YAAY1O,KAAKyO,QAAQ,CAAC9K,KAAKkH,mBAAmBc,GAAG,CAAC4C;wBAC5DD,gBAAgB,CAAC,GAAG,EAAEI,UAAU,KAAK,EAAEF,QAAQ,GAAG,CAAC;oBACrD;oBACA5I,YAAY+I,iBAAiB,CAAC,qBAAuB,CAAvB,IAAIC,MAAMN,eAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsB;gBACtD,OAAO,IAAIF,mBAAmB,GAAG;oBAC/BxI,YAAYiJ,mBAAmB;oBAC/B,MAAMlK,qBAAqBnB,MAAM,kBAAkB2D;gBACrD;YACF;YAEA+C,+BAA+BS;YAE/B,IAAImE;YACJ,IAAIjL,WAAWgD,YAAY,CAACkI,kBAAkB,EAAE;gBAC9CD,sBAAsBvN,yBACpByN,OAAO1D,IAAI,CAACb,WACZ5G,WAAWgD,YAAY,CAACoI,2BAA2B,GAC/C,AAAC,CAAA,AAACpL,WAAmBqL,kBAAkB,IAAI,EAAE,AAAD,EAAGjL,MAAM,CACnD,CAACkL,IAAW,CAACA,EAAEC,QAAQ,IAEzB,EAAE,EACNvL,WAAWgD,YAAY,CAACwI,6BAA6B;gBAGvD,IACE,CAACpF,+BACD1C,KAAKC,SAAS,CAACyC,iCACb1C,KAAKC,SAAS,CAACsH,sBACjB;oBACAhE,YAAY;oBACZb,8BAA8B6E;gBAChC;YACF;YAEA,IAAI,CAACrL,mBAAmBuG,mBAAmB;gBACzC,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAMzG,iBAAiBC,MACpB8L,IAAI,CAAC;oBACJvE,iBAAiB;gBACnB,GACCwE,KAAK,CAAC,KAAO;YAClB;YAEA,IAAIzE,aAAaC,gBAAgB;oBAyE/BnF;gBAxEA,IAAIkF,WAAW;wBAWUjH;oBAVvB,MAAM,EAAE2L,cAAc,EAAE,GAAGrP,cACzBwD,KACA8L,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBrP,KACA,MACA,CAACsP;wBACCtP,IAAIuP,IAAI,CAAC,CAAC,YAAY,EAAED,aAAa;oBACvC;oBAGF,IAAInM,qBAAmBI,2BAAAA,WAAWgD,YAAY,qBAAvBhD,yBAAyBiM,QAAQ,GAAE;wBACxD,0DAA0D;wBAC1DhN,qBAAqB;4BACnBc;4BACA4L,gBAAgB;mCACXA;gCACH;oCACExP,MAAM6D,WAAWkM,cAAc;oCAC/BL,KAAK7L,WAAW6L,GAAG;oCACnBM,UAAU;gCACZ;6BACD;wBACH;oBACF;oBAEA,MAAMrL,qBAAqBnB,MAAM,iBAAiB;wBAChD;4BAAEyM,KAAK;4BAAMC,aAAa;4BAAMC,QAAQ;wBAAK;qBAC9C;gBACH;gBACA,IAAIC;gBAIJ,IAAIrF,gBAAgB;oBAClB,IAAI;wBACFqF,iBAAiB,MAAM3P,aAAakD,KAAKE;oBAC3C,EAAE,OAAO8E,GAAG;oBACV,2EAA2E,GAC7E;gBACF;gBAEA,IAAI/C,YAAYyK,gBAAgB,EAAE;wBAO9B7M;oBANF,MAAM8M,cACJ9M,KAAK6C,SAAS,CAACD,QAAQ,CAACmK,UAAU,CAAC1H,MAAM,GAAG,KAC5CrF,KAAK6C,SAAS,CAACD,QAAQ,CAACoK,WAAW,CAAC3H,MAAM,GAAG,KAC7CrF,KAAK6C,SAAS,CAACD,QAAQ,CAACqK,QAAQ,CAAC5H,MAAM,GAAG;oBAE5C,MAAM6H,WACJlN,EAAAA,6BAAAA,KAAKK,UAAU,CAAC8M,SAAS,qBAAzBnN,2BAA2BoN,IAAI,KAC/BpN,KAAKK,UAAU,CAACgN,qBAAqB,IACrCrN,KAAKG,GAAG;oBACV,MAAMiC,YAAYyK,gBAAgB,CAACS,MAAM,CAAC;wBACxCC,WAAWnR,gBAAgB;4BACzBoR,aAAa;4BACblC;4BACA/I,QAAQlC;4BACRoM,KAAK;4BACLrM;4BACAqN,qBACEzN,KAAKK,UAAU,CAACgD,YAAY,CAACoK,mBAAmB;4BAClDX;4BACA,kBAAkB;4BAClBjG,oBAAoBlD;4BACpB+J,aAAa1N,KAAKG,GAAG;4BACrByC,UAAU5C,KAAK6C,SAAS,CAACD,QAAQ;wBACnC;wBACAsK;wBACAQ,aAAa5N,cAActD,KAAKyO,QAAQ,CAACiC,UAAU/M;oBACrD;gBACF;iBAEAiC,oCAAAA,YAAYuL,oBAAoB,qBAAhCvL,kCAAkCwL,OAAO,CAAC,CAACrL,QAAQsL;oBACjD,MAAMC,WAAWD,QAAQ;oBACzB,MAAME,eAAeF,QAAQ;oBAC7B,MAAMG,eAAeH,QAAQ;oBAC7B,MAAMf,cACJ9M,KAAK6C,SAAS,CAACD,QAAQ,CAACmK,UAAU,CAAC1H,MAAM,GAAG,KAC5CrF,KAAK6C,SAAS,CAACD,QAAQ,CAACoK,WAAW,CAAC3H,MAAM,GAAG,KAC7CrF,KAAK6C,SAAS,CAACD,QAAQ,CAACqK,QAAQ,CAAC5H,MAAM,GAAG;oBAE5C,IAAIkC,gBAAgB;4BAClBhF,yBAAAA;yBAAAA,kBAAAA,OAAOyC,OAAO,sBAAdzC,0BAAAA,gBAAgB0L,OAAO,qBAAvB1L,wBAAyBqL,OAAO,CAAC,CAACM;4BAChC,mDAAmD;4BACnD,kCAAkC;4BAClC,IAAIA,kBAAkB3O,uBAAuBqN,gBAAgB;oCAGlCrK,yBAAAA,iBAqBrB4L;gCAvBJ,MAAM,EAAEC,eAAe,EAAED,QAAQ,EAAE,GAAGvB;gCACtC,MAAMyB,yBAAyBH,OAAOE,eAAe;gCACrD,MAAME,oBAAmB/L,kBAAAA,OAAOyC,OAAO,sBAAdzC,0BAAAA,gBAAgBgM,OAAO,qBAAvBhM,wBAAyBiM,SAAS,CACzD,CAACnK,OAASA,UAASgK,0CAAAA,uBAAwBI,OAAO;gCAGpD,IAAIL,iBAAiB;oCACnB,IACEA,gBAAgBK,OAAO,MAAKJ,0CAAAA,uBAAwBI,OAAO,GAC3D;wCACA,qCAAqC;wCACrC,IAAIH,oBAAoBA,mBAAmB,CAAC,GAAG;gDAC7C/L,0BAAAA;6CAAAA,mBAAAA,OAAOyC,OAAO,sBAAdzC,2BAAAA,iBAAgBgM,OAAO,qBAAvBhM,yBAAyBmM,MAAM,CAACJ,kBAAkB;wCACpD;wCAEA,wEAAwE;wCACxE,mEAAmE;wCACnE,IAAI,CAACF,gBAAgBO,UAAU,EAAE;gDAC/BpM,0BAAAA;6CAAAA,mBAAAA,OAAOyC,OAAO,sBAAdzC,2BAAAA,iBAAgBgM,OAAO,qBAAvBhM,yBAAyBuD,IAAI,CAACsI,gBAAgBK,OAAO;wCACvD;oCACF;gCACF;gCAEA,IAAIN,CAAAA,6BAAAA,4BAAAA,SAAUS,eAAe,qBAAzBT,0BAA2BU,KAAK,KAAIT,iBAAiB;oCACvD5C,OAAO1D,IAAI,CAACoG,OAAOW,KAAK,EAAEjB,OAAO,CAAC,CAACkB;wCACjC,OAAOZ,OAAOW,KAAK,CAACC,IAAI;oCAC1B;oCACAtD,OAAOuD,MAAM,CAACb,OAAOW,KAAK,EAAEV,SAASS,eAAe,CAACC,KAAK;oCAC1DX,OAAOE,eAAe,GAAGA;gCAC3B;4BACF;wBACF;oBACF;oBAEA,IAAI9G,WAAW;4BACb/E;yBAAAA,kBAAAA,OAAO0L,OAAO,qBAAd1L,gBAAgBqL,OAAO,CAAC,CAACM;4BACvB,qDAAqD;4BACrD,sCAAsC;4BACtC,IACEA,UACA,OAAOA,OAAOc,WAAW,KAAK,YAC9Bd,OAAOc,WAAW,CAACC,iBAAiB,EACpC;gCACA,MAAMC,YAAYrP,aAAa;oCAC7B2N,aAAa;oCACblC;oCACA/I,QAAQlC;oCACRoM,KAAK;oCACLrM;oCACAqN,qBACEzN,KAAKK,UAAU,CAACgD,YAAY,CAACoK,mBAAmB;oCAClDX;oCACAgB;oCACAE;oCACAD;oCACAlH,oBAAoBlD;oCACpB+J,aAAa1N,KAAKG,GAAG;oCACrByC,UAAU5C,KAAK6C,SAAS,CAACD,QAAQ;gCACnC;gCAEA4I,OAAO1D,IAAI,CAACoG,OAAOc,WAAW,EAAEpB,OAAO,CAAC,CAACkB;oCACvC,IAAI,CAAEA,CAAAA,OAAOI,SAAQ,GAAI;wCACvB,OAAOhB,OAAOc,WAAW,CAACF,IAAI;oCAChC;gCACF;gCACAtD,OAAOuD,MAAM,CAACb,OAAOc,WAAW,EAAEE;4BACpC;wBACF;oBACF;gBACF;gBACA,MAAM9M,YAAY+M,UAAU,CAAC;oBAC3BC,yBAAyB9H;gBAC3B;YACF;YAEA,IAAI5B,iBAAiBL,MAAM,GAAG,GAAG;gBAC/BvI,IAAIyM,KAAK,CACP,qBAIC,CAJD,IAAI5K,sBACF+G,kBACAvF,KACCI,YAAYC,SAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIA,GAAE6O,OAAO;gBAEX3J,mBAAmB,EAAE;YACvB;YAEA,sEAAsE;YACtEzD,aAAaqN,aAAa,GAAG9D,OAAO+D,WAAW,CAC7C/D,OAAOgE,OAAO,CAACvI,UAAUrB,GAAG,CAAC,CAAC,CAAC6J,GAAGC,EAAE,GAAK;oBAACD;oBAAGC,EAAE3H,IAAI;iBAAG;YAExD,MAAM5G,qBACJnB,MACA,iBACAiC,aAAaqN,aAAa;YAG5B,gDAAgD;YAChDrN,aAAawH,UAAU,GAAG5C,qBACtB;gBACE8I,OAAO;gBACPlL,MAAM;gBACNiF,UAAU7C;YACZ,IACAlD;YAEJ,MAAMxC,qBAAqBnB,MAAM,cAAciC,aAAawH,UAAU;YACtExH,aAAa2N,cAAc,GAAGnI;YAE9BzH,KAAK6C,SAAS,CAACgN,iBAAiB,GAAG5N,EAAAA,2BAAAA,aAAawH,UAAU,qBAAvBxH,yBAAyByH,QAAQ,IAChEjL,2BAA0BwD,4BAAAA,aAAawH,UAAU,qBAAvBxH,0BAAyByH,QAAQ,IAC3D/F;YAEJ,MAAMmM,qBAAqB7R,mCACzBuN,OAAO1D,IAAI,CAACb,WACZjH,KAAKK,UAAU,CAACkD,QAAQ,EACxBqC,GAAG,CAAC,CAACvB,OACLxH,iBACE,wBACAwH,MACArE,KAAKK,UAAU,CAACkD,QAAQ,EACxBvD,KAAKK,UAAU,CAACgD,YAAY,CAACC,mBAAmB;YAIpDtD,KAAK6C,SAAS,CAACD,QAAQ,CAACoK,WAAW,CAAClH,IAAI,IAAIgK;YAE5C,MAAMC,gBACJ,AAAC,OAAO1P,WAAW0P,aAAa,KAAK,cAClC,OAAM1P,WAAW0P,aAAa,oBAAxB1P,WAAW0P,aAAa,MAAxB1P,YACL,CAAC,GACD;gBACEoM,KAAK;gBACLtM,KAAKH,KAAKG,GAAG;gBACb6P,QAAQ;gBACR5P,SAASA;gBACToC,SAAS;YACX,OAEJ,CAAC;YAEH,MAAMyN,uBAAuBzE,OAAOgE,OAAO,CAACO,iBAAiB,CAAC;YAE9D,IAAIE,qBAAqB5K,MAAM,GAAG,GAAG;gBACnCrF,KAAK6C,SAAS,CAACqN,mBAAmB,GAAGD,qBAAqBrK,GAAG,CAC3D,CAAC,CAACkJ,KAAKqB,MAAM,GACXtT,iBACE,wBACA;wBACEuT,QAAQtB;wBACRuB,aAAa,GAAGF,MAAM1L,IAAI,GACxB0L,MAAMG,KAAK,GAAG,MAAM,KACnB7T,GAAGuH,SAAS,CAACmM,MAAMG,KAAK,GAAG;oBAChC,GACAtQ,KAAKK,UAAU,CAACkD,QAAQ,EACxBvD,KAAKK,UAAU,CAACgD,YAAY,CAACC,mBAAmB;YAGxD;YAEA,IAAI;gBACF,gEAAgE;gBAChE,qEAAqE;gBACrE,kEAAkE;gBAClE,MAAMiN,eAAelT,gBAAgByJ;gBAErC9G,KAAK6C,SAAS,CAAC2N,aAAa,GAAGD,aAAa3K,GAAG,CAC7C,CAACnB;oBACC,MAAMgM,QAAQ/S,cAAc+G;oBAC5B,OAAO;wBACLgM,OAAOA,MAAMC,EAAE,CAACC,QAAQ;wBACxBhB,OAAO9R,gBAAgB4S;wBACvBhM;oBACF;gBACF;gBAGF,MAAMmM,aAAkD,EAAE;gBAE1D,KAAK,MAAMnM,QAAQ8L,aAAc;oBAC/B,MAAMM,QAAQjT,eAAe6G,MAAM;oBACnC,MAAMqM,aAAapT,cAAcmT,MAAMpM,IAAI;oBAC3CmM,WAAW9K,IAAI,CAAC;wBACd,GAAG+K,KAAK;wBACRJ,OAAOK,WAAWJ,EAAE,CAACC,QAAQ;wBAC7BhB,OAAO9R,gBAAgB;4BACrB,+DAA+D;4BAC/D,uCAAuC;4BACvC6S,IAAI1Q,KAAKK,UAAU,CAACqD,IAAI,GACpB,IAAIqN,OACFF,MAAMG,cAAc,CAAC9G,OAAO,CAC1B,CAAC,aAAa,CAAC,EACf,CAAC,mCAAmC,CAAC,KAGzC,IAAI6G,OAAOF,MAAMG,cAAc;4BACnCC,QAAQH,WAAWG,MAAM;wBAC3B;oBACF;gBACF;gBACAjR,KAAK6C,SAAS,CAAC2N,aAAa,CAACU,OAAO,IAAIN;gBAExC,IAAI,EAAC9L,oCAAAA,iBAAkBqM,KAAK,CAAC,CAACC,KAAKvD,MAAQuD,QAAQb,YAAY,CAAC1C,IAAI,IAAG;oBACrE,MAAMwD,cAAcd,aAAa9P,MAAM,CACrC,CAACoQ,QAAU,CAAC/L,iBAAiBmD,QAAQ,CAAC4I;oBAExC,MAAMS,gBAAgBxM,iBAAiBrE,MAAM,CAC3C,CAACoQ,QAAU,CAACN,aAAatI,QAAQ,CAAC4I;oBAGpC,8CAA8C;oBAC9CzO,YAAYmP,IAAI,CAAC;wBACfC,QAAQxS,4BAA4ByS,yBAAyB;wBAC7DC,MAAM;4BACJ;gCACEC,kBAAkB;4BACpB;yBACD;oBACH;oBAEAN,YAAYzD,OAAO,CAAC,CAACiD;wBACnBzO,YAAYmP,IAAI,CAAC;4BACfC,QAAQxS,4BAA4B4S,UAAU;4BAC9CF,MAAM;gCAACb;6BAAM;wBACf;oBACF;oBAEAS,cAAc1D,OAAO,CAAC,CAACiD;wBACrBzO,YAAYmP,IAAI,CAAC;4BACfC,QAAQxS,4BAA4B6S,YAAY;4BAChDH,MAAM;gCAACb;6BAAM;wBACf;oBACF;gBACF;gBACA/L,mBAAmByL;gBAEnB,IAAI,CAAC1L,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF,EAAE,OAAOiN,GAAG;gBACV,IAAI,CAACjN,UAAU;oBACbI,OAAO6M;oBACPjN,WAAW;gBACb,OAAO;oBACL/H,IAAIiV,IAAI,CAAC,oCAAoCD;gBAC/C;YACF,SAAU;gBACR,kEAAkE;gBAClE,4DAA4D;gBAC5D,MAAM3Q,qBAAqBnB,MAAM,kBAAkB2D;YACrD;QACF;QAEAqC,GAAGgM,KAAK,CAAC;YAAExM,aAAa;gBAACrF;aAAI;YAAE8R,WAAW;QAAE;IAC9C;IAEA,MAAMC,0BAA0B,CAAC,OAAO,EAAEhU,yBAAyB,aAAa,EAAEC,2BAA2B;IAC7G6B,KAAK6C,SAAS,CAACsP,iBAAiB,CAACrI,GAAG,CAACoI;IAErC,MAAME,4BAA4B,CAAC,OAAO,EAAElU,yBAAyB,aAAa,EAAEE,gCAAgC;IACpH4B,KAAK6C,SAAS,CAACsP,iBAAiB,CAACrI,GAAG,CAACsI;IAErC,MAAMC,qCAAqC,CAAC,OAAO,EAAEnU,yBAAyB,aAAa,EAAEI,sCAAsC;IACnI0B,KAAK6C,SAAS,CAACsP,iBAAiB,CAACrI,GAAG,CAACuI;IAErC,eAAeC,eAAeC,GAAoB,EAAEC,GAAmB;YAGjEC,qBAcFA,sBACAA;QAjBF,MAAMA,YAAYlW,IAAImW,KAAK,CAACH,IAAIhW,GAAG,IAAI;QAEvC,KAAIkW,sBAAAA,UAAUvM,QAAQ,qBAAlBuM,oBAAoBxK,QAAQ,CAACiK,0BAA0B;YACzDM,IAAIG,UAAU,GAAG;YACjBH,IAAII,SAAS,CAAC,gBAAgB;YAC9BJ,IAAIK,GAAG,CACL9O,KAAKC,SAAS,CAAC;gBACbsB,OAAOR,iBAAiBrE,MAAM,CAC5B,CAACoQ,QAAU,CAAC7Q,KAAK6C,SAAS,CAAC6E,QAAQ,CAACgD,GAAG,CAACmG;YAE5C;YAEF,OAAO;gBAAEiC,UAAU;YAAK;QAC1B;QAEA,IACEL,EAAAA,uBAAAA,UAAUvM,QAAQ,qBAAlBuM,qBAAoBxK,QAAQ,CAACmK,iCAC7BK,uBAAAA,UAAUvM,QAAQ,qBAAlBuM,qBAAoBxK,QAAQ,CAACoK,sCAC7B;gBAGuBpQ;YAFvBuQ,IAAIG,UAAU,GAAG;YACjBH,IAAII,SAAS,CAAC,gBAAgB;YAC9BJ,IAAIK,GAAG,CAAC9O,KAAKC,SAAS,CAAC/B,EAAAA,2BAAAA,aAAawH,UAAU,qBAAvBxH,yBAAyByH,QAAQ,KAAI,EAAE;YAC9D,OAAO;gBAAEoJ,UAAU;YAAK;QAC1B;QACA,OAAO;YAAEA,UAAU;QAAM;IAC3B;IAEA,SAASC,0BACPC,GAAY,EACZ1O,IAAyE;QAEzE,IAAI0O,eAAerT,kBAAkB;YACnC,wDAAwD;YACxD7C,IAAIyM,KAAK,CAACyJ,IAAI3D,OAAO;QACvB,OAAO,IAAI2D,eAAepT,wBAAwB;QAChD,yEAAyE;QACzE,mEAAmE;QACrE,OAAO,IAAI0E,SAAS,WAAW;YAC7BxH,IAAIiV,IAAI,CAACiB;QACX,OAAO,IAAI1O,SAAS,WAAW;YAC7BxH,IAAIyM,KAAK,CAACyJ;QACZ,OAAO,IAAI1O,MAAM;YACfxH,IAAIyM,KAAK,CAAC,GAAGjF,KAAK,CAAC,CAAC,EAAE0O;QACxB,OAAO;YACLlW,IAAIyM,KAAK,CAACyJ;QACZ;IACF;IAEA,OAAO;QACL/Q;QACAG;QACAkQ;QACAS;QAEA,MAAME,kBAAiBC,UAAmB;YACxC,IAAI,CAACjR,aAAauH,oBAAoB,EAAE;YACxC,OAAOpH,YAAYmC,UAAU,CAAC;gBAC5BE,MAAMxC,aAAauH,oBAAoB;gBACvChF,YAAY;gBACZI,YAAYjB;gBACZpH,KAAK2W;YACP;QACF;IACF;AACF;AAEA,OAAO,eAAeC,gBAAgBnT,IAAe;IACnD,MAAMsC,WAAW9F,KACdyO,QAAQ,CAACjL,KAAKG,GAAG,EAAEH,KAAKO,QAAQ,IAAIP,KAAKQ,MAAM,IAAI,IACnD4F,UAAU,CAAC;IAEd,MAAMgN,SAAS,MAAM5R,aAAa;QAChC,GAAGxB,IAAI;QACPsC;IACF;IAEAtC,KAAK2C,SAAS,CAAC0Q,MAAM,CACnBjW,gBACEZ,KAAKmF,IAAI,CAAC3B,KAAKG,GAAG,EAAEH,KAAKK,UAAU,CAACD,OAAO,GAC3CJ,KAAKK,UAAU,EACf;QACEiT,gBAAgB;QAChBhR;QACAiR,WAAW,CAAC,CAACvT,KAAKqC,KAAK;QACvBmR,YAAY;QACZhT,QAAQ,CAAC,CAACR,KAAKQ,MAAM;QACrBD,UAAU,CAAC,CAACP,KAAKO,QAAQ;QACzBkT,gBAAgB,CAAC,CAACzT,KAAKyT,cAAc;QACrCC,YAAY,CAAC,CAAE,MAAM9W,OAAO,YAAY;YAAE+W,KAAK3T,KAAKG,GAAG;QAAC;IAC1D;IAIJ,4CAA4C;IAC5CH,KAAK2C,SAAS,CAAC0Q,MAAM,CAAC;QACpBO,WAAWzW;QACX0W,SAAS;YACPC,aAAa;YACbC,iBAAiBrU,2BAA2BM,KAAKK,UAAU,IAAI,IAAI;QACrE;IACF;IAEA,OAAO+S;AACT;CAIA,2DAA2D", "ignoreList": [0]}