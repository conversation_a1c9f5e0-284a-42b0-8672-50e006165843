{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-turbopack.ts"], "sourcesContent": ["import type { Socket } from 'net'\nimport { mkdir, writeFile } from 'fs/promises'\nimport { join, extname, relative } from 'path'\nimport { pathToFileURL } from 'url'\n\nimport ws from 'next/dist/compiled/ws'\n\nimport type { OutputState } from '../../build/output/store'\nimport { store as consoleStore } from '../../build/output/store'\nimport type {\n  CompilationError,\n  HMR_ACTION_TYPES,\n  NextJsHotReloaderInterface,\n  ReloadPageAction,\n  SyncAction,\n  TurbopackConnectedAction,\n} from './hot-reloader-types'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport type {\n  Update as TurbopackUpdate,\n  Endpoint,\n  WrittenEndpoint,\n  TurbopackResult,\n  Project,\n  Entrypoints,\n} from '../../build/swc/types'\nimport { createDefineEnv } from '../../build/swc'\nimport * as Log from '../../build/output/log'\nimport {\n  getVersionInfo,\n  matchNextPageBundleRequest,\n} from './hot-reloader-webpack'\nimport { BLOCKED_PAGES } from '../../shared/lib/constants'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from './middleware-turbopack'\nimport { PageNotFoundError } from '../../shared/lib/utils'\nimport { debounce } from '../utils'\nimport { deleteCache, deleteFromRequireCache } from './require-cache'\nimport {\n  clearAllModuleContexts,\n  clearModuleContext,\n} from '../lib/render-server'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { trace } from '../../trace'\nimport {\n  AssetMapper,\n  type ChangeSubscriptions,\n  type ClientState,\n  handleEntrypoints,\n  handlePagesErrorRoute,\n  handleRouteType,\n  hasEntrypointForKey,\n  msToNs,\n  type ReadyIds,\n  type SendHmr,\n  type StartBuilding,\n  processTopLevelIssues,\n  printNonFatalIssue,\n  normalizedPageToTurbopackStructureRoute,\n} from './turbopack-utils'\nimport {\n  propagateServerField,\n  type ServerFields,\n  type SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { findPagePathData } from './on-demand-entry-handler'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { generateEncryptionKeyBase64 } from '../app-render/encryption-utils-server'\nimport { isAppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport type { ModernSourceMapPayload } from '../lib/source-maps'\nimport { getNodeDebugType } from '../lib/utils'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport { setBundlerFindSourceMapImplementation } from '../patch-error-inspect'\nimport { getNextErrorFeedbackMiddleware } from '../../next-devtools/server/get-next-error-feedback-middleware'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isWellKnownError,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\nimport { getDevOverlayFontMiddleware } from '../../next-devtools/server/font/get-dev-overlay-font-middleware'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\nimport { getDisableDevIndicatorMiddleware } from '../../next-devtools/server/dev-indicator-middleware'\nimport { getRestartDevServerMiddleware } from '../../next-devtools/server/restart-dev-server-middleware'\nimport { backgroundLogCompilationEvents } from '../../shared/lib/turbopack/compilation-events'\nimport { getSupportedBrowsers } from '../../build/utils'\nimport { receiveBrowserLogsTurbopack } from './browser-logs/receive-logs'\nimport { normalizePath } from '../../lib/normalize-path'\n\nconst wsServer = new ws.Server({ noServer: true })\nconst isTestMode = !!(\n  process.env.NEXT_TEST_MODE ||\n  process.env.__NEXT_TEST_MODE ||\n  process.env.DEBUG\n)\n\nconst sessionId = Math.floor(Number.MAX_SAFE_INTEGER * Math.random())\n\n/**\n * Replaces turbopack:///[project] with the specified project in the `source` field.\n */\nfunction rewriteTurbopackSources(\n  projectRoot: string,\n  sourceMap: ModernSourceMapPayload\n): void {\n  if ('sections' in sourceMap) {\n    for (const section of sourceMap.sections) {\n      rewriteTurbopackSources(projectRoot, section.map)\n    }\n  } else {\n    for (let i = 0; i < sourceMap.sources.length; i++) {\n      sourceMap.sources[i] = pathToFileURL(\n        join(\n          projectRoot,\n          sourceMap.sources[i].replace(/turbopack:\\/\\/\\/\\[project\\]/, '')\n        )\n      ).toString()\n    }\n  }\n}\n\nfunction getSourceMapFromTurbopack(\n  project: Project,\n  projectRoot: string,\n  sourceURL: string\n): ModernSourceMapPayload | undefined {\n  let sourceMapJson: string | null = null\n\n  try {\n    sourceMapJson = project.getSourceMapSync(sourceURL)\n  } catch (err) {}\n\n  if (sourceMapJson === null) {\n    return undefined\n  } else {\n    const payload: ModernSourceMapPayload = JSON.parse(sourceMapJson)\n    // The sourcemap from Turbopack is not yet written to disk so its `sources`\n    // are not absolute paths yet. We need to rewrite them to be absolute paths.\n    rewriteTurbopackSources(projectRoot, payload)\n    return payload\n  }\n}\n\nexport async function createHotReloaderTurbopack(\n  opts: SetupOpts & { isSrcDir: boolean },\n  serverFields: ServerFields,\n  distDir: string,\n  resetFetch: () => void\n): Promise<NextJsHotReloaderInterface> {\n  const dev = true\n  const buildId = 'development'\n  const { nextConfig, dir: projectPath } = opts\n\n  const { loadBindings } =\n    require('../../build/swc') as typeof import('../../build/swc')\n\n  let bindings = await loadBindings()\n\n  // For the debugging purpose, check if createNext or equivalent next instance setup in test cases\n  // works correctly. Normally `run-test` hides output so only will be visible when `--debug` flag is used.\n  if (isTestMode) {\n    ;(require('console') as typeof import('console')).log(\n      'Creating turbopack project',\n      {\n        dir: projectPath,\n        testMode: isTestMode,\n      }\n    )\n  }\n\n  const hasRewrites =\n    opts.fsChecker.rewrites.afterFiles.length > 0 ||\n    opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n    opts.fsChecker.rewrites.fallback.length > 0\n\n  const hotReloaderSpan = trace('hot-reloader', undefined, {\n    version: process.env.__NEXT_VERSION as string,\n  })\n  // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n  // of the current `next dev` invocation.\n  hotReloaderSpan.stop()\n\n  const encryptionKey = await generateEncryptionKeyBase64({\n    isBuild: false,\n    distDir,\n  })\n\n  // TODO: Implement\n  let clientRouterFilters: any\n  if (nextConfig.experimental.clientRouterFilter) {\n    // TODO this need to be set correctly for persistent caching to work\n  }\n\n  const supportedBrowsers = await getSupportedBrowsers(projectPath, dev)\n  const currentNodeJsVersion = process.versions.node\n\n  const rootPath =\n    opts.nextConfig.turbopack?.root ||\n    opts.nextConfig.outputFileTracingRoot ||\n    projectPath\n  const project = await bindings.turbo.createProject(\n    {\n      rootPath,\n      projectPath: normalizePath(relative(rootPath, projectPath) || '.'),\n      distDir,\n      nextConfig: opts.nextConfig,\n      jsConfig: await getTurbopackJsConfig(projectPath, nextConfig),\n      watch: {\n        enable: dev,\n        pollIntervalMs: nextConfig.watchOptions?.pollIntervalMs,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters,\n        config: nextConfig,\n        dev,\n        distDir,\n        projectPath,\n        fetchCacheKeyPrefix: opts.nextConfig.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // TODO: Implement\n        middlewareMatchers: undefined,\n        rewrites: opts.fsChecker.rewrites,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps: opts.fsChecker.prerenderManifest.preview,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling: false,\n      currentNodeJsVersion,\n    },\n    {\n      persistentCaching: isPersistentCachingEnabled(opts.nextConfig),\n      memoryLimit: opts.nextConfig.experimental?.turbopackMemoryLimit,\n    }\n  )\n  backgroundLogCompilationEvents(project, {\n    eventTypes: ['StartupCacheInvalidationEvent'],\n  })\n  setBundlerFindSourceMapImplementation(\n    getSourceMapFromTurbopack.bind(null, project, projectPath)\n  )\n  opts.onDevServerCleanup?.(async () => {\n    setBundlerFindSourceMapImplementation(() => undefined)\n    await project.onExit()\n  })\n  const entrypointsSubscription = project.entrypointsSubscribe()\n\n  const currentWrittenEntrypoints: Map<EntryKey, WrittenEndpoint> = new Map()\n  const currentEntrypoints: Entrypoints = {\n    global: {\n      app: undefined,\n      document: undefined,\n      error: undefined,\n\n      middleware: undefined,\n      instrumentation: undefined,\n    },\n\n    page: new Map(),\n    app: new Map(),\n  }\n\n  const currentTopLevelIssues: TopLevelIssuesMap = new Map()\n  const currentEntryIssues: EntryIssuesMap = new Map()\n\n  const manifestLoader = new TurbopackManifestLoader({\n    buildId,\n    distDir,\n    encryptionKey,\n  })\n\n  // Dev specific\n  const changeSubscriptions: ChangeSubscriptions = new Map()\n  const serverPathState = new Map<string, string>()\n  const readyIds: ReadyIds = new Set()\n  let currentEntriesHandlingResolve: ((value?: unknown) => void) | undefined\n  let currentEntriesHandling = new Promise(\n    (resolve) => (currentEntriesHandlingResolve = resolve)\n  )\n\n  const assetMapper = new AssetMapper()\n\n  function clearRequireCache(\n    key: EntryKey,\n    writtenEndpoint: WrittenEndpoint,\n    {\n      force,\n    }: {\n      // Always clear the cache, don't check if files have changed\n      force?: boolean\n    } = {}\n  ): boolean {\n    if (force) {\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        serverPathState.set(path, contentHash)\n      }\n    } else {\n      // Figure out if the server files have changed\n      let hasChange = false\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        // We ignore source maps\n        if (path.endsWith('.map')) continue\n        const localKey = `${key}:${path}`\n        const localHash = serverPathState.get(localKey)\n        const globalHash = serverPathState.get(path)\n        if (\n          (localHash && localHash !== contentHash) ||\n          (globalHash && globalHash !== contentHash)\n        ) {\n          hasChange = true\n          serverPathState.set(key, contentHash)\n          serverPathState.set(path, contentHash)\n        } else {\n          if (!localHash) {\n            serverPathState.set(key, contentHash)\n          }\n          if (!globalHash) {\n            serverPathState.set(path, contentHash)\n          }\n        }\n      }\n\n      if (!hasChange) {\n        return false\n      }\n    }\n\n    resetFetch()\n\n    const hasAppPaths = writtenEndpoint.serverPaths.some(({ path: p }) =>\n      p.startsWith('server/app')\n    )\n\n    if (hasAppPaths) {\n      deleteFromRequireCache(\n        require.resolve(\n          'next/dist/compiled/next-server/app-page-turbo.runtime.dev.js'\n        )\n      )\n      deleteFromRequireCache(\n        require.resolve(\n          'next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js'\n        )\n      )\n    }\n\n    const serverPaths = writtenEndpoint.serverPaths.map(({ path: p }) =>\n      join(distDir, p)\n    )\n\n    for (const file of serverPaths) {\n      clearModuleContext(file)\n      deleteCache(file)\n    }\n\n    return true\n  }\n\n  const buildingIds = new Set()\n\n  const startBuilding: StartBuilding = (id, requestUrl, forceRebuild) => {\n    if (!forceRebuild && readyIds.has(id)) {\n      return () => {}\n    }\n    if (buildingIds.size === 0) {\n      consoleStore.setState(\n        {\n          loading: true,\n          trigger: id,\n          url: requestUrl,\n        } as OutputState,\n        true\n      )\n    }\n    buildingIds.add(id)\n    return function finishBuilding() {\n      if (buildingIds.size === 0) {\n        return\n      }\n      readyIds.add(id)\n      buildingIds.delete(id)\n      if (buildingIds.size === 0) {\n        hmrEventHappened = false\n        consoleStore.setState(\n          {\n            loading: false,\n          } as OutputState,\n          true\n        )\n      }\n    }\n  }\n\n  let hmrEventHappened = false\n  let hmrHash = 0\n\n  const clients = new Set<ws>()\n  const clientStates = new WeakMap<ws, ClientState>()\n\n  function sendToClient(client: ws, payload: HMR_ACTION_TYPES) {\n    client.send(JSON.stringify(payload))\n  }\n\n  function sendEnqueuedMessages() {\n    for (const [, issueMap] of currentEntryIssues) {\n      if (\n        [...issueMap.values()].filter((i) => i.severity !== 'warning').length >\n        0\n      ) {\n        // During compilation errors we want to delay the HMR events until errors are fixed\n        return\n      }\n    }\n\n    for (const client of clients) {\n      const state = clientStates.get(client)\n      if (!state) {\n        continue\n      }\n\n      for (const [, issueMap] of state.clientIssues) {\n        if (\n          [...issueMap.values()].filter((i) => i.severity !== 'warning')\n            .length > 0\n        ) {\n          // During compilation errors we want to delay the HMR events until errors are fixed\n          return\n        }\n      }\n\n      for (const payload of state.hmrPayloads.values()) {\n        sendToClient(client, payload)\n      }\n      state.hmrPayloads.clear()\n\n      if (state.turbopackUpdates.length > 0) {\n        sendToClient(client, {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: state.turbopackUpdates,\n        })\n        state.turbopackUpdates.length = 0\n      }\n    }\n  }\n  const sendEnqueuedMessagesDebounce = debounce(sendEnqueuedMessages, 2)\n\n  const sendHmr: SendHmr = (id: string, payload: HMR_ACTION_TYPES) => {\n    for (const client of clients) {\n      clientStates.get(client)?.hmrPayloads.set(id, payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  function sendTurbopackMessage(payload: TurbopackUpdate) {\n    // TODO(PACK-2049): For some reason we end up emitting hundreds of issues messages on bigger apps,\n    //   a lot of which are duplicates.\n    //   They are currently not handled on the client at all, so might as well not send them for now.\n    payload.diagnostics = []\n    payload.issues = []\n\n    for (const client of clients) {\n      clientStates.get(client)?.turbopackUpdates.push(payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  async function subscribeToChanges(\n    key: EntryKey,\n    includeIssues: boolean,\n    endpoint: Endpoint,\n    makePayload: (\n      change: TurbopackResult,\n      hash: string\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n    onError?: (\n      error: Error\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n  ) {\n    if (changeSubscriptions.has(key)) {\n      return\n    }\n\n    const { side } = splitEntryKey(key)\n\n    const changedPromise = endpoint[`${side}Changed`](includeIssues)\n    changeSubscriptions.set(key, changedPromise)\n    try {\n      const changed = await changedPromise\n\n      for await (const change of changed) {\n        processIssues(currentEntryIssues, key, change, false, true)\n        // TODO: Get an actual content hash from Turbopack.\n        const payload = await makePayload(change, String(++hmrHash))\n        if (payload) {\n          sendHmr(key, payload)\n        }\n      }\n    } catch (e) {\n      changeSubscriptions.delete(key)\n      const payload = await onError?.(e as Error)\n      if (payload) {\n        sendHmr(key, payload)\n      }\n      return\n    }\n    changeSubscriptions.delete(key)\n  }\n\n  async function unsubscribeFromChanges(key: EntryKey) {\n    const subscription = await changeSubscriptions.get(key)\n    if (subscription) {\n      await subscription.return?.()\n      changeSubscriptions.delete(key)\n    }\n    currentEntryIssues.delete(key)\n  }\n\n  async function subscribeToHmrEvents(client: ws, id: string) {\n    const key = getEntryKey('assets', 'client', id)\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      // maybe throw an error / force the client to reload?\n      return\n    }\n\n    const state = clientStates.get(client)\n    if (!state || state.subscriptions.has(id)) {\n      return\n    }\n\n    const subscription = project!.hmrEvents(id)\n    state.subscriptions.set(id, subscription)\n\n    // The subscription will always emit once, which is the initial\n    // computation. This is not a change, so swallow it.\n    try {\n      await subscription.next()\n\n      for await (const data of subscription) {\n        processIssues(state.clientIssues, key, data, false, true)\n        if (data.type !== 'issues') {\n          sendTurbopackMessage(data)\n        }\n      }\n    } catch (e) {\n      // The client might be using an HMR session from a previous server, tell them\n      // to fully reload the page to resolve the issue. We can't use\n      // `hotReloader.send` since that would force every connected client to\n      // reload, only this client is out of date.\n      const reloadAction: ReloadPageAction = {\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: `error in HMR event subscription for ${id}: ${e}`,\n      }\n      sendToClient(client, reloadAction)\n      client.close()\n      return\n    }\n  }\n\n  function unsubscribeFromHmrEvents(client: ws, id: string) {\n    const state = clientStates.get(client)\n    if (!state) {\n      return\n    }\n\n    const subscription = state.subscriptions.get(id)\n    subscription?.return!()\n\n    const key = getEntryKey('assets', 'client', id)\n    state.clientIssues.delete(key)\n  }\n\n  async function handleEntrypointsSubscription() {\n    for await (const entrypoints of entrypointsSubscription) {\n      if (!currentEntriesHandlingResolve) {\n        currentEntriesHandling = new Promise(\n          // eslint-disable-next-line no-loop-func\n          (resolve) => (currentEntriesHandlingResolve = resolve)\n        )\n      }\n\n      processTopLevelIssues(currentTopLevelIssues, entrypoints)\n\n      await handleEntrypoints({\n        entrypoints,\n\n        currentEntrypoints,\n\n        currentEntryIssues,\n        manifestLoader,\n        devRewrites: opts.fsChecker.rewrites,\n        productionRewrites: undefined,\n        logErrors: true,\n\n        dev: {\n          assetMapper,\n          changeSubscriptions,\n          clients,\n          clientStates,\n          serverFields,\n\n          hooks: {\n            handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n              currentWrittenEntrypoints.set(id, result)\n              return clearRequireCache(id, result, { force: forceDeleteCache })\n            },\n            propagateServerField: propagateServerField.bind(null, opts),\n            sendHmr,\n            startBuilding,\n            subscribeToChanges,\n            unsubscribeFromChanges,\n            unsubscribeFromHmrEvents,\n          },\n        },\n      })\n\n      currentEntriesHandlingResolve!()\n      currentEntriesHandlingResolve = undefined\n    }\n  }\n\n  await mkdir(join(distDir, 'server'), { recursive: true })\n  await mkdir(join(distDir, 'static', buildId), { recursive: true })\n  await writeFile(\n    join(distDir, 'package.json'),\n    JSON.stringify(\n      {\n        type: 'commonjs',\n      },\n      null,\n      2\n    )\n  )\n\n  const middlewares = [\n    getOverlayMiddleware({\n      project,\n      projectPath,\n      isSrcDir: opts.isSrcDir,\n    }),\n    getSourceMapMiddleware(project),\n    getNextErrorFeedbackMiddleware(opts.telemetry),\n    getDevOverlayFontMiddleware(),\n    getDisableDevIndicatorMiddleware(),\n    getRestartDevServerMiddleware({\n      telemetry: opts.telemetry,\n      turbopackProject: project,\n    }),\n  ]\n\n  const versionInfoPromise = getVersionInfo()\n\n  let devtoolsFrontendUrl: string | undefined\n  const nodeDebugType = getNodeDebugType()\n  if (nodeDebugType) {\n    const debugPort = process.debugPort\n    let debugInfo\n    try {\n      // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n      const debugInfoList = await fetch(\n        `http://127.0.0.1:${debugPort}/json/list`\n      ).then((res) => res.json())\n      debugInfo = debugInfoList[0]\n    } catch {}\n    if (debugInfo) {\n      devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n    }\n  }\n\n  const hotReloader: NextJsHotReloaderInterface = {\n    turbopackProject: project,\n    activeWebpackConfigs: undefined,\n    serverStats: null,\n    edgeServerStats: null,\n    async run(req, res, _parsedUrl) {\n      // intercept page chunks request and ensure them with turbopack\n      if (req.url?.startsWith('/_next/static/chunks/pages/')) {\n        const params = matchNextPageBundleRequest(req.url)\n\n        if (params) {\n          const decodedPagePath = `/${params.path\n            .map((param: string) => decodeURIComponent(param))\n            .join('/')}`\n\n          const denormalizedPagePath = denormalizePagePath(decodedPagePath)\n\n          await hotReloader\n            .ensurePage({\n              page: denormalizedPagePath,\n              clientOnly: false,\n              definition: undefined,\n              url: req.url,\n            })\n            .catch(console.error)\n        }\n      }\n\n      for (const middleware of middlewares) {\n        let calledNext = false\n\n        await middleware(req, res, () => {\n          calledNext = true\n        })\n\n        if (!calledNext) {\n          return { finished: true }\n        }\n      }\n\n      // Request was not finished.\n      return { finished: undefined }\n    },\n\n    // TODO: Figure out if socket type can match the NextJsHotReloaderInterface\n    onHMR(req, socket: Socket, head, onUpgrade) {\n      wsServer.handleUpgrade(req, socket, head, (client) => {\n        onUpgrade(client)\n        const clientIssues: EntryIssuesMap = new Map()\n        const subscriptions: Map<string, AsyncIterator<any>> = new Map()\n\n        clients.add(client)\n        clientStates.set(client, {\n          clientIssues,\n          hmrPayloads: new Map(),\n          turbopackUpdates: [],\n          subscriptions,\n        })\n\n        client.on('close', () => {\n          // Remove active subscriptions\n          for (const subscription of subscriptions.values()) {\n            subscription.return?.()\n          }\n          clientStates.delete(client)\n          clients.delete(client)\n        })\n\n        client.addEventListener('message', async ({ data }) => {\n          const parsedData = JSON.parse(\n            typeof data !== 'string' ? data.toString() : data\n          )\n\n          // Next.js messages\n          switch (parsedData.event) {\n            case 'span-end': {\n              hotReloaderSpan.manualTraceChild(\n                parsedData.spanName,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                parsedData.attributes\n              )\n              break\n            }\n            case 'client-hmr-latency': // { id, startTime, endTime, page, updatedModules, isPageHidden }\n              hotReloaderSpan.manualTraceChild(\n                parsedData.event,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                {\n                  updatedModules: parsedData.updatedModules,\n                  page: parsedData.page,\n                  isPageHidden: parsedData.isPageHidden,\n                }\n              )\n              break\n\n            case 'client-error': // { errorCount, clientId }\n            case 'client-warning': // { warningCount, clientId }\n            case 'client-success': // { clientId }\n            case 'server-component-reload-page': // { clientId }\n            case 'client-reload-page': // { clientId }\n            case 'client-removed-page': // { page }\n            case 'client-full-reload': // { stackTrace, hadRuntimeError }\n              const { hadRuntimeError, dependencyChain } = parsedData\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n              }\n              if (\n                Array.isArray(dependencyChain) &&\n                typeof dependencyChain[0] === 'string'\n              ) {\n                const cleanedModulePath = dependencyChain[0]\n                  .replace(/^\\[project\\]/, '.')\n                  .replace(/ \\[.*\\] \\(.*\\)$/, '')\n                Log.warn(\n                  `Fast Refresh had to perform a full reload when ${cleanedModulePath} changed. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n                )\n              }\n              break\n            case 'client-added-page':\n              // TODO\n              break\n            case 'browser-logs': {\n              if (nextConfig.experimental.browserDebugInfoInTerminal) {\n                await receiveBrowserLogsTurbopack({\n                  entries: parsedData.entries,\n                  router: parsedData.router,\n                  sourceType: parsedData.sourceType,\n                  project,\n                  projectPath,\n                  distDir,\n                  config: nextConfig.experimental.browserDebugInfoInTerminal,\n                })\n              }\n              break\n            }\n\n            default:\n              // Might be a Turbopack message...\n              if (!parsedData.type) {\n                throw new Error(`unrecognized HMR message \"${data}\"`)\n              }\n          }\n\n          // Turbopack messages\n          switch (parsedData.type) {\n            case 'turbopack-subscribe':\n              subscribeToHmrEvents(client, parsedData.path)\n              break\n\n            case 'turbopack-unsubscribe':\n              unsubscribeFromHmrEvents(client, parsedData.path)\n              break\n\n            default:\n              if (!parsedData.event) {\n                throw new Error(`unrecognized Turbopack HMR message \"${data}\"`)\n              }\n          }\n        })\n\n        const turbopackConnected: TurbopackConnectedAction = {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: { sessionId },\n        }\n        sendToClient(client, turbopackConnected)\n\n        const errors: CompilationError[] = []\n\n        for (const entryIssues of currentEntryIssues.values()) {\n          for (const issue of entryIssues.values()) {\n            if (issue.severity !== 'warning') {\n              errors.push({\n                message: formatIssue(issue),\n              })\n            } else {\n              printNonFatalIssue(issue)\n            }\n          }\n        }\n\n        if (devIndicatorServerState.disabledUntil < Date.now()) {\n          devIndicatorServerState.disabledUntil = 0\n        }\n\n        ;(async function () {\n          const versionInfo = await versionInfoPromise\n\n          const sync: SyncAction = {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC,\n            errors,\n            warnings: [],\n            hash: '',\n            versionInfo,\n            debug: {\n              devtoolsFrontendUrl,\n            },\n            devIndicator: devIndicatorServerState,\n          }\n\n          sendToClient(client, sync)\n        })()\n      })\n    },\n\n    send(action) {\n      const payload = JSON.stringify(action)\n      for (const client of clients) {\n        client.send(payload)\n      }\n    },\n\n    setHmrServerError(_error) {\n      // Not implemented yet.\n    },\n    clearHmrServerError() {\n      // Not implemented yet.\n    },\n    async start() {},\n    async getCompilationErrors(page) {\n      const appEntryKey = getEntryKey('app', 'server', page)\n      const pagesEntryKey = getEntryKey('pages', 'server', page)\n\n      const topLevelIssues = currentTopLevelIssues.values()\n\n      const thisEntryIssues =\n        currentEntryIssues.get(appEntryKey) ??\n        currentEntryIssues.get(pagesEntryKey)\n\n      if (thisEntryIssues !== undefined && thisEntryIssues.size > 0) {\n        // If there is an error related to the requesting page we display it instead of the first error\n        return [...topLevelIssues, ...thisEntryIssues.values()]\n          .map((issue) => {\n            const formattedIssue = formatIssue(issue)\n            if (issue.severity === 'warning') {\n              printNonFatalIssue(issue)\n              return null\n            } else if (isWellKnownError(issue)) {\n              Log.error(formattedIssue)\n            }\n\n            return new Error(formattedIssue)\n          })\n          .filter((error) => error !== null)\n      }\n\n      // Otherwise, return all errors across pages\n      const errors = []\n      for (const issue of topLevelIssues) {\n        if (issue.severity !== 'warning') {\n          errors.push(new Error(formatIssue(issue)))\n        }\n      }\n      for (const entryIssues of currentEntryIssues.values()) {\n        for (const issue of entryIssues.values()) {\n          if (issue.severity !== 'warning') {\n            const message = formatIssue(issue)\n            errors.push(new Error(message))\n          } else {\n            printNonFatalIssue(issue)\n          }\n        }\n      }\n      return errors\n    },\n    async invalidate({\n      // .env files or tsconfig/jsconfig change\n      reloadAfterInvalidation,\n    }) {\n      if (reloadAfterInvalidation) {\n        for (const [key, entrypoint] of currentWrittenEntrypoints) {\n          clearRequireCache(key, entrypoint, { force: true })\n        }\n\n        await clearAllModuleContexts()\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n          hash: String(++hmrHash),\n        })\n      }\n    },\n    async buildFallbackError() {\n      // Not implemented yet.\n    },\n    async ensurePage({\n      page: inputPage,\n      // Unused parameters\n      // clientOnly,\n      appPaths,\n      definition,\n      isApp,\n      url: requestUrl,\n    }) {\n      // When there is no route definition this is an internal file not a route the user added.\n      // Middleware and instrumentation are handled in turbpack-utils.ts handleEntrypoints instead.\n      if (!definition) {\n        if (inputPage === '/middleware') return\n        if (inputPage === '/src/middleware') return\n        if (inputPage === '/instrumentation') return\n        if (inputPage === '/src/instrumentation') return\n      }\n\n      return hotReloaderSpan\n        .traceChild('ensure-page', {\n          inputPage,\n        })\n        .traceAsyncFn(async () => {\n          if (BLOCKED_PAGES.includes(inputPage) && inputPage !== '/_error') {\n            return\n          }\n\n          await currentEntriesHandling\n\n          // TODO We shouldn't look into the filesystem again. This should use the information from entrypoints\n          let routeDef: Pick<\n            RouteDefinition,\n            'filename' | 'bundlePath' | 'page'\n          > =\n            definition ??\n            (await findPagePathData(\n              projectPath,\n              inputPage,\n              nextConfig.pageExtensions,\n              opts.pagesDir,\n              opts.appDir,\n              !!nextConfig.experimental.globalNotFound\n            ))\n\n          // If the route is actually an app page route, then we should have access\n          // to the app route definition, and therefore, the appPaths from it.\n          if (!appPaths && definition && isAppPageRouteDefinition(definition)) {\n            appPaths = definition.appPaths\n          }\n\n          let page = routeDef.page\n          if (appPaths) {\n            const normalizedPage = normalizeAppPath(page)\n\n            // filter out paths that are not exact matches (e.g. catchall)\n            const matchingAppPaths = appPaths.filter(\n              (path) => normalizeAppPath(path) === normalizedPage\n            )\n\n            // the last item in the array is the root page, if there are parallel routes\n            page = matchingAppPaths[matchingAppPaths.length - 1]\n          }\n\n          const pathname = definition?.pathname ?? inputPage\n\n          if (page === '/_error') {\n            let finishBuilding = startBuilding(pathname, requestUrl, false)\n            try {\n              await handlePagesErrorRoute({\n                currentEntryIssues,\n                entrypoints: currentEntrypoints,\n                manifestLoader,\n                devRewrites: opts.fsChecker.rewrites,\n                productionRewrites: undefined,\n                logErrors: true,\n                hooks: {\n                  subscribeToChanges,\n                  handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n                    currentWrittenEntrypoints.set(id, result)\n                    assetMapper.setPathsForKey(id, result.clientPaths)\n                    return clearRequireCache(id, result, {\n                      force: forceDeleteCache,\n                    })\n                  },\n                },\n              })\n            } finally {\n              finishBuilding()\n            }\n            return\n          }\n\n          const isInsideAppDir = routeDef.bundlePath.startsWith('app/')\n          const isEntryMetadataRouteFile = isMetadataRouteFile(\n            routeDef.filename.replace(opts.appDir || '', ''),\n            nextConfig.pageExtensions,\n            true\n          )\n          const normalizedAppPage = isEntryMetadataRouteFile\n            ? normalizedPageToTurbopackStructureRoute(\n                page,\n                extname(routeDef.filename)\n              )\n            : page\n\n          const route = isInsideAppDir\n            ? currentEntrypoints.app.get(normalizedAppPage)\n            : currentEntrypoints.page.get(page)\n\n          if (!route) {\n            // TODO: why is this entry missing in turbopack?\n            if (page === '/middleware') return\n            if (page === '/src/middleware') return\n            if (page === '/instrumentation') return\n            if (page === '/src/instrumentation') return\n\n            throw new PageNotFoundError(`route not found ${page}`)\n          }\n\n          // We don't throw on ensureOpts.isApp === true for page-api\n          // since this can happen when app pages make\n          // api requests to page API routes.\n          if (isApp && route.type === 'page') {\n            throw new Error(`mis-matched route type: isApp && page for ${page}`)\n          }\n\n          const finishBuilding = startBuilding(pathname, requestUrl, false)\n          try {\n            await handleRouteType({\n              dev,\n              page,\n              pathname,\n              route,\n              currentEntryIssues,\n              entrypoints: currentEntrypoints,\n              manifestLoader,\n              readyIds,\n              devRewrites: opts.fsChecker.rewrites,\n              productionRewrites: undefined,\n              logErrors: true,\n\n              hooks: {\n                subscribeToChanges,\n                handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n                  currentWrittenEntrypoints.set(id, result)\n                  assetMapper.setPathsForKey(id, result.clientPaths)\n                  return clearRequireCache(id, result, {\n                    force: forceDeleteCache,\n                  })\n                },\n              },\n            })\n          } finally {\n            finishBuilding()\n          }\n        })\n    },\n    close() {\n      for (const wsClient of clients) {\n        // it's okay to not cleanly close these websocket connections, this is dev\n        wsClient.terminate()\n      }\n      clients.clear()\n    },\n  }\n\n  handleEntrypointsSubscription().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  // Write empty manifests\n  await currentEntriesHandling\n  await manifestLoader.writeManifests({\n    devRewrites: opts.fsChecker.rewrites,\n    productionRewrites: undefined,\n    entrypoints: currentEntrypoints,\n  })\n\n  async function handleProjectUpdates() {\n    for await (const updateMessage of project.updateInfoSubscribe(30)) {\n      switch (updateMessage.updateType) {\n        case 'start': {\n          hotReloader.send({ action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING })\n          break\n        }\n        case 'end': {\n          sendEnqueuedMessages()\n\n          function addErrors(\n            errorsMap: Map<string, CompilationError>,\n            issues: EntryIssuesMap\n          ) {\n            for (const issueMap of issues.values()) {\n              for (const [key, issue] of issueMap) {\n                if (issue.severity === 'warning') continue\n                if (errorsMap.has(key)) continue\n\n                const message = formatIssue(issue)\n\n                errorsMap.set(key, {\n                  message,\n                  details: issue.detail\n                    ? renderStyledStringToErrorAnsi(issue.detail)\n                    : undefined,\n                })\n              }\n            }\n          }\n\n          const errors = new Map<string, CompilationError>()\n          addErrors(errors, currentEntryIssues)\n\n          for (const client of clients) {\n            const state = clientStates.get(client)\n            if (!state) {\n              continue\n            }\n\n            const clientErrors = new Map(errors)\n            addErrors(clientErrors, state.clientIssues)\n\n            sendToClient(client, {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT,\n              hash: String(++hmrHash),\n              errors: [...clientErrors.values()],\n              warnings: [],\n            })\n          }\n\n          if (hmrEventHappened) {\n            const time = updateMessage.value.duration\n            const timeMessage =\n              time > 2000 ? `${Math.round(time / 100) / 10}s` : `${time}ms`\n            Log.event(`Compiled in ${timeMessage}`)\n            hmrEventHappened = false\n          }\n          break\n        }\n        default:\n      }\n    }\n  }\n\n  handleProjectUpdates().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  return hotReloader\n}\n"], "names": ["mkdir", "writeFile", "join", "extname", "relative", "pathToFileURL", "ws", "store", "consoleStore", "HMR_ACTIONS_SENT_TO_BROWSER", "createDefineEnv", "Log", "getVersionInfo", "matchNextPageBundleRequest", "BLOCKED_PAGES", "getOverlayMiddleware", "getSourceMapMiddleware", "PageNotFoundError", "debounce", "deleteCache", "deleteFromRequireCache", "clearAllModuleContexts", "clearModuleContext", "denormalizePagePath", "trace", "AssetMapper", "handleEntrypoints", "handlePagesErrorRoute", "handleRouteType", "hasEntrypointForKey", "msToNs", "processTopLevelIssues", "printNonFatalIssue", "normalizedPageToTurbopackStructureRoute", "propagateServerField", "TurbopackManifestLoader", "findPagePathData", "getEntry<PERSON>ey", "splitEntryKey", "FAST_REFRESH_RUNTIME_RELOAD", "generateEncryptionKeyBase64", "isAppPageRouteDefinition", "normalizeAppPath", "getNodeDebugType", "isMetadataRouteFile", "setBundlerFindSourceMapImplementation", "getNextErrorFeedbackMiddleware", "formatIssue", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isWellKnownError", "processIssues", "renderStyledStringToErrorAnsi", "getDevOverlayFontMiddleware", "devIndicatorServerState", "getDisableDevIndicatorMiddleware", "getRestartDevServerMiddleware", "backgroundLogCompilationEvents", "getSupportedBrowsers", "receiveBrowserLogsTurbopack", "normalizePath", "wsServer", "Server", "noServer", "isTestMode", "process", "env", "NEXT_TEST_MODE", "__NEXT_TEST_MODE", "DEBUG", "sessionId", "Math", "floor", "Number", "MAX_SAFE_INTEGER", "random", "rewriteTurbopackSources", "projectRoot", "sourceMap", "section", "sections", "map", "i", "sources", "length", "replace", "toString", "getSourceMapFromTurbopack", "project", "sourceURL", "sourceMapJson", "getSourceMapSync", "err", "undefined", "payload", "JSON", "parse", "createHotReloaderTurbopack", "opts", "serverFields", "distDir", "resetFetch", "nextConfig", "dev", "buildId", "dir", "projectPath", "loadBindings", "require", "bindings", "log", "testMode", "hasRewrites", "fs<PERSON><PERSON><PERSON>", "rewrites", "afterFiles", "beforeFiles", "fallback", "hotReloaderSpan", "version", "__NEXT_VERSION", "stop", "<PERSON><PERSON><PERSON>", "isBuild", "clientRouterFilters", "experimental", "clientRouterFilter", "supportedBrowsers", "currentNodeJsVersion", "versions", "node", "rootPath", "turbopack", "root", "outputFileTracingRoot", "turbo", "createProject", "jsConfig", "watch", "enable", "pollIntervalMs", "watchOptions", "defineEnv", "isTurbopack", "config", "fetchCacheKeyPrefix", "middlewareMatchers", "previewProps", "prerenderManifest", "preview", "browserslistQuery", "noMangling", "persistentCaching", "memoryLimit", "turbopackMemoryLimit", "eventTypes", "bind", "onDevServerCleanup", "onExit", "entrypointsSubscription", "entrypointsSubscribe", "currentWrittenEntrypoints", "Map", "currentEntrypoints", "global", "app", "document", "error", "middleware", "instrumentation", "page", "currentTopLevelIssues", "currentEntryIssues", "manifest<PERSON><PERSON>der", "changeSubscriptions", "serverPathState", "readyIds", "Set", "currentEntriesHandlingResolve", "currentEntriesHandling", "Promise", "resolve", "assetMapper", "clearRequireCache", "key", "writtenEndpoint", "force", "path", "contentHash", "serverPaths", "set", "hasChange", "endsWith", "localKey", "localHash", "get", "globalHash", "hasAppPaths", "some", "p", "startsWith", "file", "buildingIds", "startBuilding", "id", "requestUrl", "forceRebuild", "has", "size", "setState", "loading", "trigger", "url", "add", "finishBuilding", "delete", "hmrEventHappened", "hmrHash", "clients", "clientStates", "WeakMap", "sendToClient", "client", "send", "stringify", "sendEnqueuedMessages", "issueMap", "values", "filter", "severity", "state", "clientIssues", "hmrPayloads", "clear", "turbopackUpdates", "action", "TURBOPACK_MESSAGE", "data", "sendEnqueuedMessagesDebounce", "sendHmr", "sendTurbopackMessage", "diagnostics", "issues", "push", "subscribeToChanges", "includeIssues", "endpoint", "makePayload", "onError", "side", "changedPromise", "changed", "change", "String", "e", "unsubscribeFromChanges", "subscription", "return", "subscribeToHmrEvents", "subscriptions", "hmrEvents", "next", "type", "reloadAction", "RELOAD_PAGE", "close", "unsubscribeFromHmrEvents", "handleEntrypointsSubscription", "entrypoints", "devRewrites", "productionRewrites", "logErrors", "hooks", "handleWrittenEndpoint", "result", "forceDeleteCache", "recursive", "middlewares", "isSrcDir", "telemetry", "turbopackProject", "versionInfoPromise", "devtoolsFrontendUrl", "nodeDebugType", "debugPort", "debugInfo", "debugInfoList", "fetch", "then", "res", "json", "hotReloader", "activeWebpackConfigs", "serverStats", "edgeServerStats", "run", "req", "_parsedUrl", "params", "decodedPagePath", "param", "decodeURIComponent", "denormalizedPagePath", "ensurePage", "clientOnly", "definition", "catch", "console", "calledNext", "finished", "onHMR", "socket", "head", "onUpgrade", "handleUpgrade", "on", "addEventListener", "parsedData", "event", "manualTraceChild", "spanName", "startTime", "endTime", "attributes", "updatedModules", "isPageHidden", "hadRuntimeError", "dependency<PERSON><PERSON>n", "warn", "Array", "isArray", "cleanedModulePath", "browserDebugInfoInTerminal", "entries", "router", "sourceType", "Error", "turbopackConnected", "TURBOPACK_CONNECTED", "errors", "entryIssues", "issue", "message", "disabledUntil", "Date", "now", "versionInfo", "sync", "SYNC", "warnings", "hash", "debug", "devIndicator", "setHmrServerError", "_error", "clearHmrServerError", "start", "getCompilationErrors", "appEntry<PERSON>ey", "pagesEntry<PERSON>ey", "topLevelIssues", "thisEntryIssues", "formattedIssue", "invalidate", "reloadAfterInvalidation", "entrypoint", "SERVER_COMPONENT_CHANGES", "buildFallbackError", "inputPage", "appPaths", "isApp", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "includes", "routeDef", "pageExtensions", "pagesDir", "appDir", "globalNotFound", "normalizedPage", "matchingAppPaths", "pathname", "setPathsFor<PERSON>ey", "clientPaths", "isInsideAppDir", "bundlePath", "isEntryMetadataRouteFile", "filename", "normalizedAppPage", "route", "wsClient", "terminate", "exit", "writeManifests", "handleProjectUpdates", "updateMessage", "updateInfoSubscribe", "updateType", "BUILDING", "addErrors", "errorsMap", "details", "detail", "clientErrors", "BUILT", "time", "value", "duration", "timeMessage", "round"], "mappings": "AACA,SAASA,KAAK,EAAEC,SAAS,QAAQ,cAAa;AAC9C,SAASC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAM;AAC9C,SAASC,aAAa,QAAQ,MAAK;AAEnC,OAAOC,QAAQ,wBAAuB;AAGtC,SAASC,SAASC,YAAY,QAAQ,2BAA0B;AAShE,SAASC,2BAA2B,QAAQ,uBAAsB;AASlE,SAASC,eAAe,QAAQ,kBAAiB;AACjD,YAAYC,SAAS,yBAAwB;AAC7C,SACEC,cAAc,EACdC,0BAA0B,QACrB,yBAAwB;AAC/B,SAASC,aAAa,QAAQ,6BAA4B;AAC1D,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,yBAAwB;AAC/B,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,QAAQ,QAAQ,WAAU;AACnC,SAASC,WAAW,EAAEC,sBAAsB,QAAQ,kBAAiB;AACrE,SACEC,sBAAsB,EACtBC,kBAAkB,QACb,uBAAsB;AAC7B,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,KAAK,QAAQ,cAAa;AACnC,SACEC,WAAW,EAGXC,iBAAiB,EACjBC,qBAAqB,EACrBC,eAAe,EACfC,mBAAmB,EACnBC,MAAM,EAINC,qBAAqB,EACrBC,kBAAkB,EAClBC,uCAAuC,QAClC,oBAAmB;AAC1B,SACEC,oBAAoB,QAGf,wCAAuC;AAC9C,SAASC,uBAAuB,QAAQ,6CAA4C;AACpF,SAASC,gBAAgB,QAAQ,4BAA2B;AAE5D,SAEEC,WAAW,EACXC,aAAa,QACR,uCAAsC;AAC7C,SAASC,2BAA2B,QAAQ,aAAY;AACxD,SAASC,2BAA2B,QAAQ,wCAAuC;AACnF,SAASC,wBAAwB,QAAQ,iDAAgD;AACzF,SAASC,gBAAgB,QAAQ,0CAAyC;AAE1E,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,SAASC,mBAAmB,QAAQ,uCAAsC;AAC1E,SAASC,qCAAqC,QAAQ,yBAAwB;AAC9E,SAASC,8BAA8B,QAAQ,gEAA+D;AAC9G,SACEC,WAAW,EACXC,oBAAoB,EACpBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,aAAa,EACbC,6BAA6B,QAGxB,mCAAkC;AACzC,SAASC,2BAA2B,QAAQ,kEAAiE;AAC7G,SAASC,uBAAuB,QAAQ,+BAA8B;AACtE,SAASC,gCAAgC,QAAQ,sDAAqD;AACtG,SAASC,6BAA6B,QAAQ,2DAA0D;AACxG,SAASC,8BAA8B,QAAQ,gDAA+C;AAC9F,SAASC,oBAAoB,QAAQ,oBAAmB;AACxD,SAASC,2BAA2B,QAAQ,8BAA6B;AACzE,SAASC,aAAa,QAAQ,2BAA0B;AAExD,MAAMC,WAAW,IAAIvD,GAAGwD,MAAM,CAAC;IAAEC,UAAU;AAAK;AAChD,MAAMC,aAAa,CAAC,CAClBC,CAAAA,QAAQC,GAAG,CAACC,cAAc,IAC1BF,QAAQC,GAAG,CAACE,gBAAgB,IAC5BH,QAAQC,GAAG,CAACG,KAAK,AAAD;AAGlB,MAAMC,YAAYC,KAAKC,KAAK,CAACC,OAAOC,gBAAgB,GAAGH,KAAKI,MAAM;AAElE;;CAEC,GACD,SAASC,wBACPC,WAAmB,EACnBC,SAAiC;IAEjC,IAAI,cAAcA,WAAW;QAC3B,KAAK,MAAMC,WAAWD,UAAUE,QAAQ,CAAE;YACxCJ,wBAAwBC,aAAaE,QAAQE,GAAG;QAClD;IACF,OAAO;QACL,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,UAAUK,OAAO,CAACC,MAAM,EAAEF,IAAK;YACjDJ,UAAUK,OAAO,CAACD,EAAE,GAAG7E,cACrBH,KACE2E,aACAC,UAAUK,OAAO,CAACD,EAAE,CAACG,OAAO,CAAC,+BAA+B,MAE9DC,QAAQ;QACZ;IACF;AACF;AAEA,SAASC,0BACPC,OAAgB,EAChBX,WAAmB,EACnBY,SAAiB;IAEjB,IAAIC,gBAA+B;IAEnC,IAAI;QACFA,gBAAgBF,QAAQG,gBAAgB,CAACF;IAC3C,EAAE,OAAOG,KAAK,CAAC;IAEf,IAAIF,kBAAkB,MAAM;QAC1B,OAAOG;IACT,OAAO;QACL,MAAMC,UAAkCC,KAAKC,KAAK,CAACN;QACnD,2EAA2E;QAC3E,4EAA4E;QAC5Ed,wBAAwBC,aAAaiB;QACrC,OAAOA;IACT;AACF;AAEA,OAAO,eAAeG,2BACpBC,IAAuC,EACvCC,YAA0B,EAC1BC,OAAe,EACfC,UAAsB;QAkDpBH,4BAYoBI,0BA0BLJ;IAtFjB,MAAMK,MAAM;IACZ,MAAMC,UAAU;IAChB,MAAM,EAAEF,UAAU,EAAEG,KAAKC,WAAW,EAAE,GAAGR;IAEzC,MAAM,EAAES,YAAY,EAAE,GACpBC,QAAQ;IAEV,IAAIC,WAAW,MAAMF;IAErB,iGAAiG;IACjG,yGAAyG;IACzG,IAAI3C,YAAY;;QACZ4C,QAAQ,WAAwCE,GAAG,CACnD,8BACA;YACEL,KAAKC;YACLK,UAAU/C;QACZ;IAEJ;IAEA,MAAMgD,cACJd,KAAKe,SAAS,CAACC,QAAQ,CAACC,UAAU,CAAC/B,MAAM,GAAG,KAC5Cc,KAAKe,SAAS,CAACC,QAAQ,CAACE,WAAW,CAAChC,MAAM,GAAG,KAC7Cc,KAAKe,SAAS,CAACC,QAAQ,CAACG,QAAQ,CAACjC,MAAM,GAAG;IAE5C,MAAMkC,kBAAkB9F,MAAM,gBAAgBqE,WAAW;QACvD0B,SAAStD,QAAQC,GAAG,CAACsD,cAAc;IACrC;IACA,8FAA8F;IAC9F,wCAAwC;IACxCF,gBAAgBG,IAAI;IAEpB,MAAMC,gBAAgB,MAAMlF,4BAA4B;QACtDmF,SAAS;QACTvB;IACF;IAEA,kBAAkB;IAClB,IAAIwB;IACJ,IAAItB,WAAWuB,YAAY,CAACC,kBAAkB,EAAE;IAC9C,oEAAoE;IACtE;IAEA,MAAMC,oBAAoB,MAAMrE,qBAAqBgD,aAAaH;IAClE,MAAMyB,uBAAuB/D,QAAQgE,QAAQ,CAACC,IAAI;IAElD,MAAMC,WACJjC,EAAAA,6BAAAA,KAAKI,UAAU,CAAC8B,SAAS,qBAAzBlC,2BAA2BmC,IAAI,KAC/BnC,KAAKI,UAAU,CAACgC,qBAAqB,IACrC5B;IACF,MAAMlB,UAAU,MAAMqB,SAAS0B,KAAK,CAACC,aAAa,CAChD;QACEL;QACAzB,aAAa9C,cAAcxD,SAAS+H,UAAUzB,gBAAgB;QAC9DN;QACAE,YAAYJ,KAAKI,UAAU;QAC3BmC,UAAU,MAAMzF,qBAAqB0D,aAAaJ;QAClDoC,OAAO;YACLC,QAAQpC;YACRqC,cAAc,GAAEtC,2BAAAA,WAAWuC,YAAY,qBAAvBvC,yBAAyBsC,cAAc;QACzD;QACArC;QACArC,KAAKD,QAAQC,GAAG;QAChB4E,WAAWpI,gBAAgB;YACzBqI,aAAa;YACbnB;YACAoB,QAAQ1C;YACRC;YACAH;YACAM;YACAuC,qBAAqB/C,KAAKI,UAAU,CAACuB,YAAY,CAACoB,mBAAmB;YACrEjC;YACA,kBAAkB;YAClBkC,oBAAoBrD;YACpBqB,UAAUhB,KAAKe,SAAS,CAACC,QAAQ;QACnC;QACAV;QACAkB;QACAyB,cAAcjD,KAAKe,SAAS,CAACmC,iBAAiB,CAACC,OAAO;QACtDC,mBAAmBvB,kBAAkB7H,IAAI,CAAC;QAC1CqJ,YAAY;QACZvB;IACF,GACA;QACEwB,mBAAmBvG,2BAA2BiD,KAAKI,UAAU;QAC7DmD,WAAW,GAAEvD,gCAAAA,KAAKI,UAAU,CAACuB,YAAY,qBAA5B3B,8BAA8BwD,oBAAoB;IACjE;IAEFjG,+BAA+B+B,SAAS;QACtCmE,YAAY;YAAC;SAAgC;IAC/C;IACA9G,sCACE0C,0BAA0BqE,IAAI,CAAC,MAAMpE,SAASkB;IAEhDR,KAAK2D,kBAAkB,oBAAvB3D,KAAK2D,kBAAkB,MAAvB3D,MAA0B;QACxBrD,sCAAsC,IAAMgD;QAC5C,MAAML,QAAQsE,MAAM;IACtB;IACA,MAAMC,0BAA0BvE,QAAQwE,oBAAoB;IAE5D,MAAMC,4BAA4D,IAAIC;IACtE,MAAMC,qBAAkC;QACtCC,QAAQ;YACNC,KAAKxE;YACLyE,UAAUzE;YACV0E,OAAO1E;YAEP2E,YAAY3E;YACZ4E,iBAAiB5E;QACnB;QAEA6E,MAAM,IAAIR;QACVG,KAAK,IAAIH;IACX;IAEA,MAAMS,wBAA2C,IAAIT;IACrD,MAAMU,qBAAqC,IAAIV;IAE/C,MAAMW,iBAAiB,IAAI1I,wBAAwB;QACjDqE;QACAJ;QACAsB;IACF;IAEA,eAAe;IACf,MAAMoD,sBAA2C,IAAIZ;IACrD,MAAMa,kBAAkB,IAAIb;IAC5B,MAAMc,WAAqB,IAAIC;IAC/B,IAAIC;IACJ,IAAIC,yBAAyB,IAAIC,QAC/B,CAACC,UAAaH,gCAAgCG;IAGhD,MAAMC,cAAc,IAAI7J;IAExB,SAAS8J,kBACPC,GAAa,EACbC,eAAgC,EAChC,EACEC,KAAK,EAIN,GAAG,CAAC,CAAC;QAEN,IAAIA,OAAO;YACT,KAAK,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/Dd,gBAAgBe,GAAG,CAACH,MAAMC;YAC5B;QACF,OAAO;YACL,8CAA8C;YAC9C,IAAIG,YAAY;YAChB,KAAK,MAAM,EAAEJ,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/D,wBAAwB;gBACxB,IAAIF,KAAKK,QAAQ,CAAC,SAAS;gBAC3B,MAAMC,WAAW,GAAGT,IAAI,CAAC,EAAEG,MAAM;gBACjC,MAAMO,YAAYnB,gBAAgBoB,GAAG,CAACF;gBACtC,MAAMG,aAAarB,gBAAgBoB,GAAG,CAACR;gBACvC,IACE,AAACO,aAAaA,cAAcN,eAC3BQ,cAAcA,eAAeR,aAC9B;oBACAG,YAAY;oBACZhB,gBAAgBe,GAAG,CAACN,KAAKI;oBACzBb,gBAAgBe,GAAG,CAACH,MAAMC;gBAC5B,OAAO;oBACL,IAAI,CAACM,WAAW;wBACdnB,gBAAgBe,GAAG,CAACN,KAAKI;oBAC3B;oBACA,IAAI,CAACQ,YAAY;wBACfrB,gBAAgBe,GAAG,CAACH,MAAMC;oBAC5B;gBACF;YACF;YAEA,IAAI,CAACG,WAAW;gBACd,OAAO;YACT;QACF;QAEA1F;QAEA,MAAMgG,cAAcZ,gBAAgBI,WAAW,CAACS,IAAI,CAAC,CAAC,EAAEX,MAAMY,CAAC,EAAE,GAC/DA,EAAEC,UAAU,CAAC;QAGf,IAAIH,aAAa;YACfjL,uBACEwF,QAAQyE,OAAO,CACb;YAGJjK,uBACEwF,QAAQyE,OAAO,CACb;QAGN;QAEA,MAAMQ,cAAcJ,gBAAgBI,WAAW,CAAC5G,GAAG,CAAC,CAAC,EAAE0G,MAAMY,CAAC,EAAE,GAC9DrM,KAAKkG,SAASmG;QAGhB,KAAK,MAAME,QAAQZ,YAAa;YAC9BvK,mBAAmBmL;YACnBtL,YAAYsL;QACd;QAEA,OAAO;IACT;IAEA,MAAMC,cAAc,IAAIzB;IAExB,MAAM0B,gBAA+B,CAACC,IAAIC,YAAYC;QACpD,IAAI,CAACA,gBAAgB9B,SAAS+B,GAAG,CAACH,KAAK;YACrC,OAAO,KAAO;QAChB;QACA,IAAIF,YAAYM,IAAI,KAAK,GAAG;YAC1BxM,aAAayM,QAAQ,CACnB;gBACEC,SAAS;gBACTC,SAASP;gBACTQ,KAAKP;YACP,GACA;QAEJ;QACAH,YAAYW,GAAG,CAACT;QAChB,OAAO,SAASU;YACd,IAAIZ,YAAYM,IAAI,KAAK,GAAG;gBAC1B;YACF;YACAhC,SAASqC,GAAG,CAACT;YACbF,YAAYa,MAAM,CAACX;YACnB,IAAIF,YAAYM,IAAI,KAAK,GAAG;gBAC1BQ,mBAAmB;gBACnBhN,aAAayM,QAAQ,CACnB;oBACEC,SAAS;gBACX,GACA;YAEJ;QACF;IACF;IAEA,IAAIM,mBAAmB;IACvB,IAAIC,UAAU;IAEd,MAAMC,UAAU,IAAIzC;IACpB,MAAM0C,eAAe,IAAIC;IAEzB,SAASC,aAAaC,MAAU,EAAEhI,OAAyB;QACzDgI,OAAOC,IAAI,CAAChI,KAAKiI,SAAS,CAAClI;IAC7B;IAEA,SAASmI;QACP,KAAK,MAAM,GAAGC,SAAS,IAAItD,mBAAoB;YAC7C,IACE;mBAAIsD,SAASC,MAAM;aAAG,CAACC,MAAM,CAAC,CAAClJ,IAAMA,EAAEmJ,QAAQ,KAAK,WAAWjJ,MAAM,GACrE,GACA;gBACA,mFAAmF;gBACnF;YACF;QACF;QAEA,KAAK,MAAM0I,UAAUJ,QAAS;YAC5B,MAAMY,QAAQX,aAAaxB,GAAG,CAAC2B;YAC/B,IAAI,CAACQ,OAAO;gBACV;YACF;YAEA,KAAK,MAAM,GAAGJ,SAAS,IAAII,MAAMC,YAAY,CAAE;gBAC7C,IACE;uBAAIL,SAASC,MAAM;iBAAG,CAACC,MAAM,CAAC,CAAClJ,IAAMA,EAAEmJ,QAAQ,KAAK,WACjDjJ,MAAM,GAAG,GACZ;oBACA,mFAAmF;oBACnF;gBACF;YACF;YAEA,KAAK,MAAMU,WAAWwI,MAAME,WAAW,CAACL,MAAM,GAAI;gBAChDN,aAAaC,QAAQhI;YACvB;YACAwI,MAAME,WAAW,CAACC,KAAK;YAEvB,IAAIH,MAAMI,gBAAgB,CAACtJ,MAAM,GAAG,GAAG;gBACrCyI,aAAaC,QAAQ;oBACnBa,QAAQlO,4BAA4BmO,iBAAiB;oBACrDC,MAAMP,MAAMI,gBAAgB;gBAC9B;gBACAJ,MAAMI,gBAAgB,CAACtJ,MAAM,GAAG;YAClC;QACF;IACF;IACA,MAAM0J,+BAA+B5N,SAAS+M,sBAAsB;IAEpE,MAAMc,UAAmB,CAACnC,IAAY9G;QACpC,KAAK,MAAMgI,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAaxB,GAAG,CAAC2B,4BAAjBH,kBAA0Ba,WAAW,CAAC1C,GAAG,CAACc,IAAI9G;QAChD;QAEA0H,mBAAmB;QACnBsB;IACF;IAEA,SAASE,qBAAqBlJ,OAAwB;QACpD,kGAAkG;QAClG,mCAAmC;QACnC,iGAAiG;QACjGA,QAAQmJ,WAAW,GAAG,EAAE;QACxBnJ,QAAQoJ,MAAM,GAAG,EAAE;QAEnB,KAAK,MAAMpB,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAaxB,GAAG,CAAC2B,4BAAjBH,kBAA0Be,gBAAgB,CAACS,IAAI,CAACrJ;QAClD;QAEA0H,mBAAmB;QACnBsB;IACF;IAEA,eAAeM,mBACb5D,GAAa,EACb6D,aAAsB,EACtBC,QAAkB,EAClBC,WAGwD,EACxDC,OAEwD;QAExD,IAAI1E,oBAAoBiC,GAAG,CAACvB,MAAM;YAChC;QACF;QAEA,MAAM,EAAEiE,IAAI,EAAE,GAAGnN,cAAckJ;QAE/B,MAAMkE,iBAAiBJ,QAAQ,CAAC,GAAGG,KAAK,OAAO,CAAC,CAAC,CAACJ;QAClDvE,oBAAoBgB,GAAG,CAACN,KAAKkE;QAC7B,IAAI;YACF,MAAMC,UAAU,MAAMD;YAEtB,WAAW,MAAME,UAAUD,QAAS;gBAClCxM,cAAcyH,oBAAoBY,KAAKoE,QAAQ,OAAO;gBACtD,mDAAmD;gBACnD,MAAM9J,UAAU,MAAMyJ,YAAYK,QAAQC,OAAO,EAAEpC;gBACnD,IAAI3H,SAAS;oBACXiJ,QAAQvD,KAAK1F;gBACf;YACF;QACF,EAAE,OAAOgK,GAAG;YACVhF,oBAAoByC,MAAM,CAAC/B;YAC3B,MAAM1F,UAAU,OAAM0J,2BAAAA,QAAUM;YAChC,IAAIhK,SAAS;gBACXiJ,QAAQvD,KAAK1F;YACf;YACA;QACF;QACAgF,oBAAoByC,MAAM,CAAC/B;IAC7B;IAEA,eAAeuE,uBAAuBvE,GAAa;QACjD,MAAMwE,eAAe,MAAMlF,oBAAoBqB,GAAG,CAACX;QACnD,IAAIwE,cAAc;YAChB,OAAMA,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;YACNlF,oBAAoByC,MAAM,CAAC/B;QAC7B;QACAZ,mBAAmB2C,MAAM,CAAC/B;IAC5B;IAEA,eAAe0E,qBAAqBpC,MAAU,EAAElB,EAAU;QACxD,MAAMpB,MAAMnJ,YAAY,UAAU,UAAUuK;QAC5C,IAAI,CAAC/K,oBAAoBsI,oBAAoBqB,KAAKF,cAAc;YAC9D,qDAAqD;YACrD;QACF;QAEA,MAAMgD,QAAQX,aAAaxB,GAAG,CAAC2B;QAC/B,IAAI,CAACQ,SAASA,MAAM6B,aAAa,CAACpD,GAAG,CAACH,KAAK;YACzC;QACF;QAEA,MAAMoD,eAAexK,QAAS4K,SAAS,CAACxD;QACxC0B,MAAM6B,aAAa,CAACrE,GAAG,CAACc,IAAIoD;QAE5B,+DAA+D;QAC/D,oDAAoD;QACpD,IAAI;YACF,MAAMA,aAAaK,IAAI;YAEvB,WAAW,MAAMxB,QAAQmB,aAAc;gBACrC7M,cAAcmL,MAAMC,YAAY,EAAE/C,KAAKqD,MAAM,OAAO;gBACpD,IAAIA,KAAKyB,IAAI,KAAK,UAAU;oBAC1BtB,qBAAqBH;gBACvB;YACF;QACF,EAAE,OAAOiB,GAAG;YACV,6EAA6E;YAC7E,8DAA8D;YAC9D,sEAAsE;YACtE,2CAA2C;YAC3C,MAAMS,eAAiC;gBACrC5B,QAAQlO,4BAA4B+P,WAAW;gBAC/C3B,MAAM,CAAC,oCAAoC,EAAEjC,GAAG,EAAE,EAAEkD,GAAG;YACzD;YACAjC,aAAaC,QAAQyC;YACrBzC,OAAO2C,KAAK;YACZ;QACF;IACF;IAEA,SAASC,yBAAyB5C,MAAU,EAAElB,EAAU;QACtD,MAAM0B,QAAQX,aAAaxB,GAAG,CAAC2B;QAC/B,IAAI,CAACQ,OAAO;YACV;QACF;QAEA,MAAM0B,eAAe1B,MAAM6B,aAAa,CAAChE,GAAG,CAACS;QAC7CoD,gCAAAA,aAAcC,MAAM;QAEpB,MAAMzE,MAAMnJ,YAAY,UAAU,UAAUuK;QAC5C0B,MAAMC,YAAY,CAAChB,MAAM,CAAC/B;IAC5B;IAEA,eAAemF;QACb,WAAW,MAAMC,eAAe7G,wBAAyB;YACvD,IAAI,CAACmB,+BAA+B;gBAClCC,yBAAyB,IAAIC,QAC3B,wCAAwC;gBACxC,CAACC,UAAaH,gCAAgCG;YAElD;YAEAtJ,sBAAsB4I,uBAAuBiG;YAE7C,MAAMlP,kBAAkB;gBACtBkP;gBAEAzG;gBAEAS;gBACAC;gBACAgG,aAAa3K,KAAKe,SAAS,CAACC,QAAQ;gBACpC4J,oBAAoBjL;gBACpBkL,WAAW;gBAEXxK,KAAK;oBACH+E;oBACAR;oBACA4C;oBACAC;oBACAxH;oBAEA6K,OAAO;wBACLC,uBAAuB,CAACrE,IAAIsE,QAAQC;4BAClClH,0BAA0B6B,GAAG,CAACc,IAAIsE;4BAClC,OAAO3F,kBAAkBqB,IAAIsE,QAAQ;gCAAExF,OAAOyF;4BAAiB;wBACjE;wBACAjP,sBAAsBA,qBAAqB0H,IAAI,CAAC,MAAM1D;wBACtD6I;wBACApC;wBACAyC;wBACAW;wBACAW;oBACF;gBACF;YACF;YAEAxF;YACAA,gCAAgCrF;QAClC;IACF;IAEA,MAAM7F,MAAME,KAAKkG,SAAS,WAAW;QAAEgL,WAAW;IAAK;IACvD,MAAMpR,MAAME,KAAKkG,SAAS,UAAUI,UAAU;QAAE4K,WAAW;IAAK;IAChE,MAAMnR,UACJC,KAAKkG,SAAS,iBACdL,KAAKiI,SAAS,CACZ;QACEsC,MAAM;IACR,GACA,MACA;IAIJ,MAAMe,cAAc;QAClBtQ,qBAAqB;YACnByE;YACAkB;YACA4K,UAAUpL,KAAKoL,QAAQ;QACzB;QACAtQ,uBAAuBwE;QACvB1C,+BAA+BoD,KAAKqL,SAAS;QAC7ClO;QACAE;QACAC,8BAA8B;YAC5B+N,WAAWrL,KAAKqL,SAAS;YACzBC,kBAAkBhM;QACpB;KACD;IAED,MAAMiM,qBAAqB7Q;IAE3B,IAAI8Q;IACJ,MAAMC,gBAAgBhP;IACtB,IAAIgP,eAAe;QACjB,MAAMC,YAAY3N,QAAQ2N,SAAS;QACnC,IAAIC;QACJ,IAAI;YACF,8EAA8E;YAC9E,MAAMC,gBAAgB,MAAMC,MAC1B,CAAC,iBAAiB,EAAEH,UAAU,UAAU,CAAC,EACzCI,IAAI,CAAC,CAACC,MAAQA,IAAIC,IAAI;YACxBL,YAAYC,aAAa,CAAC,EAAE;QAC9B,EAAE,OAAM,CAAC;QACT,IAAID,WAAW;YACbH,sBAAsBG,UAAUH,mBAAmB;QACrD;IACF;IAEA,MAAMS,cAA0C;QAC9CX,kBAAkBhM;QAClB4M,sBAAsBvM;QACtBwM,aAAa;QACbC,iBAAiB;QACjB,MAAMC,KAAIC,GAAG,EAAEP,GAAG,EAAEQ,UAAU;gBAExBD;YADJ,+DAA+D;YAC/D,KAAIA,WAAAA,IAAIpF,GAAG,qBAAPoF,SAAShG,UAAU,CAAC,gCAAgC;gBACtD,MAAMkG,SAAS7R,2BAA2B2R,IAAIpF,GAAG;gBAEjD,IAAIsF,QAAQ;oBACV,MAAMC,kBAAkB,CAAC,CAAC,EAAED,OAAO/G,IAAI,CACpC1G,GAAG,CAAC,CAAC2N,QAAkBC,mBAAmBD,QAC1C1S,IAAI,CAAC,MAAM;oBAEd,MAAM4S,uBAAuBvR,oBAAoBoR;oBAEjD,MAAMR,YACHY,UAAU,CAAC;wBACVrI,MAAMoI;wBACNE,YAAY;wBACZC,YAAYpN;wBACZuH,KAAKoF,IAAIpF,GAAG;oBACd,GACC8F,KAAK,CAACC,QAAQ5I,KAAK;gBACxB;YACF;YAEA,KAAK,MAAMC,cAAc6G,YAAa;gBACpC,IAAI+B,aAAa;gBAEjB,MAAM5I,WAAWgI,KAAKP,KAAK;oBACzBmB,aAAa;gBACf;gBAEA,IAAI,CAACA,YAAY;oBACf,OAAO;wBAAEC,UAAU;oBAAK;gBAC1B;YACF;YAEA,4BAA4B;YAC5B,OAAO;gBAAEA,UAAUxN;YAAU;QAC/B;QAEA,2EAA2E;QAC3EyN,OAAMd,GAAG,EAAEe,MAAc,EAAEC,IAAI,EAAEC,SAAS;YACxC5P,SAAS6P,aAAa,CAAClB,KAAKe,QAAQC,MAAM,CAAC1F;gBACzC2F,UAAU3F;gBACV,MAAMS,eAA+B,IAAIrE;gBACzC,MAAMiG,gBAAiD,IAAIjG;gBAE3DwD,QAAQL,GAAG,CAACS;gBACZH,aAAa7B,GAAG,CAACgC,QAAQ;oBACvBS;oBACAC,aAAa,IAAItE;oBACjBwE,kBAAkB,EAAE;oBACpByB;gBACF;gBAEArC,OAAO6F,EAAE,CAAC,SAAS;oBACjB,8BAA8B;oBAC9B,KAAK,MAAM3D,gBAAgBG,cAAchC,MAAM,GAAI;wBACjD6B,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;oBACF;oBACArC,aAAaJ,MAAM,CAACO;oBACpBJ,QAAQH,MAAM,CAACO;gBACjB;gBAEAA,OAAO8F,gBAAgB,CAAC,WAAW,OAAO,EAAE/E,IAAI,EAAE;oBAChD,MAAMgF,aAAa9N,KAAKC,KAAK,CAC3B,OAAO6I,SAAS,WAAWA,KAAKvJ,QAAQ,KAAKuJ;oBAG/C,mBAAmB;oBACnB,OAAQgF,WAAWC,KAAK;wBACtB,KAAK;4BAAY;gCACfxM,gBAAgByM,gBAAgB,CAC9BF,WAAWG,QAAQ,EACnBlS,OAAO+R,WAAWI,SAAS,GAC3BnS,OAAO+R,WAAWK,OAAO,GACzBL,WAAWM,UAAU;gCAEvB;4BACF;wBACA,KAAK;4BACH7M,gBAAgByM,gBAAgB,CAC9BF,WAAWC,KAAK,EAChBhS,OAAO+R,WAAWI,SAAS,GAC3BnS,OAAO+R,WAAWK,OAAO,GACzB;gCACEE,gBAAgBP,WAAWO,cAAc;gCACzC1J,MAAMmJ,WAAWnJ,IAAI;gCACrB2J,cAAcR,WAAWQ,YAAY;4BACvC;4BAEF;wBAEF,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,MAAM,EAAEC,eAAe,EAAEC,eAAe,EAAE,GAAGV;4BAC7C,IAAIS,iBAAiB;gCACnB3T,IAAI6T,IAAI,CAACjS;4BACX;4BACA,IACEkS,MAAMC,OAAO,CAACH,oBACd,OAAOA,eAAe,CAAC,EAAE,KAAK,UAC9B;gCACA,MAAMI,oBAAoBJ,eAAe,CAAC,EAAE,CACzClP,OAAO,CAAC,gBAAgB,KACxBA,OAAO,CAAC,mBAAmB;gCAC9B1E,IAAI6T,IAAI,CACN,CAAC,+CAA+C,EAAEG,kBAAkB,yEAAyE,CAAC;4BAElJ;4BACA;wBACF,KAAK;4BAEH;wBACF,KAAK;4BAAgB;gCACnB,IAAIrO,WAAWuB,YAAY,CAAC+M,0BAA0B,EAAE;oCACtD,MAAMjR,4BAA4B;wCAChCkR,SAAShB,WAAWgB,OAAO;wCAC3BC,QAAQjB,WAAWiB,MAAM;wCACzBC,YAAYlB,WAAWkB,UAAU;wCACjCvP;wCACAkB;wCACAN;wCACA4C,QAAQ1C,WAAWuB,YAAY,CAAC+M,0BAA0B;oCAC5D;gCACF;gCACA;4BACF;wBAEA;4BACE,kCAAkC;4BAClC,IAAI,CAACf,WAAWvD,IAAI,EAAE;gCACpB,MAAM,qBAA+C,CAA/C,IAAI0E,MAAM,CAAC,0BAA0B,EAAEnG,KAAK,CAAC,CAAC,GAA9C,qBAAA;2CAAA;gDAAA;kDAAA;gCAA8C;4BACtD;oBACJ;oBAEA,qBAAqB;oBACrB,OAAQgF,WAAWvD,IAAI;wBACrB,KAAK;4BACHJ,qBAAqBpC,QAAQ+F,WAAWlI,IAAI;4BAC5C;wBAEF,KAAK;4BACH+E,yBAAyB5C,QAAQ+F,WAAWlI,IAAI;4BAChD;wBAEF;4BACE,IAAI,CAACkI,WAAWC,KAAK,EAAE;gCACrB,MAAM,qBAAyD,CAAzD,IAAIkB,MAAM,CAAC,oCAAoC,EAAEnG,KAAK,CAAC,CAAC,GAAxD,qBAAA;2CAAA;gDAAA;kDAAA;gCAAwD;4BAChE;oBACJ;gBACF;gBAEA,MAAMoG,qBAA+C;oBACnDtG,QAAQlO,4BAA4ByU,mBAAmB;oBACvDrG,MAAM;wBAAEvK;oBAAU;gBACpB;gBACAuJ,aAAaC,QAAQmH;gBAErB,MAAME,SAA6B,EAAE;gBAErC,KAAK,MAAMC,eAAexK,mBAAmBuD,MAAM,GAAI;oBACrD,KAAK,MAAMkH,SAASD,YAAYjH,MAAM,GAAI;wBACxC,IAAIkH,MAAMhH,QAAQ,KAAK,WAAW;4BAChC8G,OAAOhG,IAAI,CAAC;gCACVmG,SAASvS,YAAYsS;4BACvB;wBACF,OAAO;4BACLrT,mBAAmBqT;wBACrB;oBACF;gBACF;gBAEA,IAAI/R,wBAAwBiS,aAAa,GAAGC,KAAKC,GAAG,IAAI;oBACtDnS,wBAAwBiS,aAAa,GAAG;gBAC1C;;gBAEE,CAAA;oBACA,MAAMG,cAAc,MAAMjE;oBAE1B,MAAMkE,OAAmB;wBACvBhH,QAAQlO,4BAA4BmV,IAAI;wBACxCT;wBACAU,UAAU,EAAE;wBACZC,MAAM;wBACNJ;wBACAK,OAAO;4BACLrE;wBACF;wBACAsE,cAAc1S;oBAChB;oBAEAuK,aAAaC,QAAQ6H;gBACvB,CAAA;YACF;QACF;QAEA5H,MAAKY,MAAM;YACT,MAAM7I,UAAUC,KAAKiI,SAAS,CAACW;YAC/B,KAAK,MAAMb,UAAUJ,QAAS;gBAC5BI,OAAOC,IAAI,CAACjI;YACd;QACF;QAEAmQ,mBAAkBC,MAAM;QACtB,uBAAuB;QACzB;QACAC;QACE,uBAAuB;QACzB;QACA,MAAMC,UAAS;QACf,MAAMC,sBAAqB3L,IAAI;YAC7B,MAAM4L,cAAcjU,YAAY,OAAO,UAAUqI;YACjD,MAAM6L,gBAAgBlU,YAAY,SAAS,UAAUqI;YAErD,MAAM8L,iBAAiB7L,sBAAsBwD,MAAM;YAEnD,MAAMsI,kBACJ7L,mBAAmBuB,GAAG,CAACmK,gBACvB1L,mBAAmBuB,GAAG,CAACoK;YAEzB,IAAIE,oBAAoB5Q,aAAa4Q,gBAAgBzJ,IAAI,GAAG,GAAG;gBAC7D,+FAA+F;gBAC/F,OAAO;uBAAIwJ;uBAAmBC,gBAAgBtI,MAAM;iBAAG,CACpDlJ,GAAG,CAAC,CAACoQ;oBACJ,MAAMqB,iBAAiB3T,YAAYsS;oBACnC,IAAIA,MAAMhH,QAAQ,KAAK,WAAW;wBAChCrM,mBAAmBqT;wBACnB,OAAO;oBACT,OAAO,IAAInS,iBAAiBmS,QAAQ;wBAClC1U,IAAI4J,KAAK,CAACmM;oBACZ;oBAEA,OAAO,qBAAyB,CAAzB,IAAI1B,MAAM0B,iBAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwB;gBACjC,GACCtI,MAAM,CAAC,CAAC7D,QAAUA,UAAU;YACjC;YAEA,4CAA4C;YAC5C,MAAM4K,SAAS,EAAE;YACjB,KAAK,MAAME,SAASmB,eAAgB;gBAClC,IAAInB,MAAMhH,QAAQ,KAAK,WAAW;oBAChC8G,OAAOhG,IAAI,CAAC,qBAA6B,CAA7B,IAAI6F,MAAMjS,YAAYsS,SAAtB,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4B;gBAC1C;YACF;YACA,KAAK,MAAMD,eAAexK,mBAAmBuD,MAAM,GAAI;gBACrD,KAAK,MAAMkH,SAASD,YAAYjH,MAAM,GAAI;oBACxC,IAAIkH,MAAMhH,QAAQ,KAAK,WAAW;wBAChC,MAAMiH,UAAUvS,YAAYsS;wBAC5BF,OAAOhG,IAAI,CAAC,qBAAkB,CAAlB,IAAI6F,MAAMM,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAC/B,OAAO;wBACLtT,mBAAmBqT;oBACrB;gBACF;YACF;YACA,OAAOF;QACT;QACA,MAAMwB,YAAW,EACf,yCAAyC;QACzCC,uBAAuB,EACxB;YACC,IAAIA,yBAAyB;gBAC3B,KAAK,MAAM,CAACpL,KAAKqL,WAAW,IAAI5M,0BAA2B;oBACzDsB,kBAAkBC,KAAKqL,YAAY;wBAAEnL,OAAO;oBAAK;gBACnD;gBAEA,MAAMrK;gBACN,IAAI,CAAC0M,IAAI,CAAC;oBACRY,QAAQlO,4BAA4BqW,wBAAwB;oBAC5DhB,MAAMjG,OAAO,EAAEpC;gBACjB;YACF;QACF;QACA,MAAMsJ;QACJ,uBAAuB;QACzB;QACA,MAAMhE,YAAW,EACfrI,MAAMsM,SAAS,EACf,oBAAoB;QACpB,cAAc;QACdC,QAAQ,EACRhE,UAAU,EACViE,KAAK,EACL9J,KAAKP,UAAU,EAChB;YACC,yFAAyF;YACzF,6FAA6F;YAC7F,IAAI,CAACoG,YAAY;gBACf,IAAI+D,cAAc,eAAe;gBACjC,IAAIA,cAAc,mBAAmB;gBACrC,IAAIA,cAAc,oBAAoB;gBACtC,IAAIA,cAAc,wBAAwB;YAC5C;YAEA,OAAO1P,gBACJ6P,UAAU,CAAC,eAAe;gBACzBH;YACF,GACCI,YAAY,CAAC;gBACZ,IAAItW,cAAcuW,QAAQ,CAACL,cAAcA,cAAc,WAAW;oBAChE;gBACF;gBAEA,MAAM7L;gBAEN,qGAAqG;gBACrG,IAAImM,WAIFrE,cACC,MAAM7Q,iBACLsE,aACAsQ,WACA1Q,WAAWiR,cAAc,EACzBrR,KAAKsR,QAAQ,EACbtR,KAAKuR,MAAM,EACX,CAAC,CAACnR,WAAWuB,YAAY,CAAC6P,cAAc;gBAG5C,yEAAyE;gBACzE,oEAAoE;gBACpE,IAAI,CAACT,YAAYhE,cAAcxQ,yBAAyBwQ,aAAa;oBACnEgE,WAAWhE,WAAWgE,QAAQ;gBAChC;gBAEA,IAAIvM,OAAO4M,SAAS5M,IAAI;gBACxB,IAAIuM,UAAU;oBACZ,MAAMU,iBAAiBjV,iBAAiBgI;oBAExC,8DAA8D;oBAC9D,MAAMkN,mBAAmBX,SAAS7I,MAAM,CACtC,CAACzC,OAASjJ,iBAAiBiJ,UAAUgM;oBAGvC,4EAA4E;oBAC5EjN,OAAOkN,gBAAgB,CAACA,iBAAiBxS,MAAM,GAAG,EAAE;gBACtD;gBAEA,MAAMyS,WAAW5E,CAAAA,8BAAAA,WAAY4E,QAAQ,KAAIb;gBAEzC,IAAItM,SAAS,WAAW;oBACtB,IAAI4C,iBAAiBX,cAAckL,UAAUhL,YAAY;oBACzD,IAAI;wBACF,MAAMlL,sBAAsB;4BAC1BiJ;4BACAgG,aAAazG;4BACbU;4BACAgG,aAAa3K,KAAKe,SAAS,CAACC,QAAQ;4BACpC4J,oBAAoBjL;4BACpBkL,WAAW;4BACXC,OAAO;gCACL5B;gCACA6B,uBAAuB,CAACrE,IAAIsE,QAAQC;oCAClClH,0BAA0B6B,GAAG,CAACc,IAAIsE;oCAClC5F,YAAYwM,cAAc,CAAClL,IAAIsE,OAAO6G,WAAW;oCACjD,OAAOxM,kBAAkBqB,IAAIsE,QAAQ;wCACnCxF,OAAOyF;oCACT;gCACF;4BACF;wBACF;oBACF,SAAU;wBACR7D;oBACF;oBACA;gBACF;gBAEA,MAAM0K,iBAAiBV,SAASW,UAAU,CAACzL,UAAU,CAAC;gBACtD,MAAM0L,2BAA2BtV,oBAC/B0U,SAASa,QAAQ,CAAC9S,OAAO,CAACa,KAAKuR,MAAM,IAAI,IAAI,KAC7CnR,WAAWiR,cAAc,EACzB;gBAEF,MAAMa,oBAAoBF,2BACtBjW,wCACEyI,MACAvK,QAAQmX,SAASa,QAAQ,KAE3BzN;gBAEJ,MAAM2N,QAAQL,iBACV7N,mBAAmBE,GAAG,CAAC8B,GAAG,CAACiM,qBAC3BjO,mBAAmBO,IAAI,CAACyB,GAAG,CAACzB;gBAEhC,IAAI,CAAC2N,OAAO;oBACV,gDAAgD;oBAChD,IAAI3N,SAAS,eAAe;oBAC5B,IAAIA,SAAS,mBAAmB;oBAChC,IAAIA,SAAS,oBAAoB;oBACjC,IAAIA,SAAS,wBAAwB;oBAErC,MAAM,IAAIzJ,kBAAkB,CAAC,gBAAgB,EAAEyJ,MAAM;gBACvD;gBAEA,2DAA2D;gBAC3D,4CAA4C;gBAC5C,mCAAmC;gBACnC,IAAIwM,SAASmB,MAAM/H,IAAI,KAAK,QAAQ;oBAClC,MAAM,qBAA8D,CAA9D,IAAI0E,MAAM,CAAC,0CAA0C,EAAEtK,MAAM,GAA7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA6D;gBACrE;gBAEA,MAAM4C,iBAAiBX,cAAckL,UAAUhL,YAAY;gBAC3D,IAAI;oBACF,MAAMjL,gBAAgB;wBACpB2E;wBACAmE;wBACAmN;wBACAQ;wBACAzN;wBACAgG,aAAazG;wBACbU;wBACAG;wBACA6F,aAAa3K,KAAKe,SAAS,CAACC,QAAQ;wBACpC4J,oBAAoBjL;wBACpBkL,WAAW;wBAEXC,OAAO;4BACL5B;4BACA6B,uBAAuB,CAACrE,IAAIsE,QAAQC;gCAClClH,0BAA0B6B,GAAG,CAACc,IAAIsE;gCAClC5F,YAAYwM,cAAc,CAAClL,IAAIsE,OAAO6G,WAAW;gCACjD,OAAOxM,kBAAkBqB,IAAIsE,QAAQ;oCACnCxF,OAAOyF;gCACT;4BACF;wBACF;oBACF;gBACF,SAAU;oBACR7D;gBACF;YACF;QACJ;QACAmD;YACE,KAAK,MAAM6H,YAAY5K,QAAS;gBAC9B,0EAA0E;gBAC1E4K,SAASC,SAAS;YACpB;YACA7K,QAAQe,KAAK;QACf;IACF;IAEAkC,gCAAgCuC,KAAK,CAAC,CAACtN;QACrCuN,QAAQ5I,KAAK,CAAC3E;QACd3B,QAAQuU,IAAI,CAAC;IACf;IAEA,wBAAwB;IACxB,MAAMrN;IACN,MAAMN,eAAe4N,cAAc,CAAC;QAClC5H,aAAa3K,KAAKe,SAAS,CAACC,QAAQ;QACpC4J,oBAAoBjL;QACpB+K,aAAazG;IACf;IAEA,eAAeuO;QACb,WAAW,MAAMC,iBAAiBnT,QAAQoT,mBAAmB,CAAC,IAAK;YACjE,OAAQD,cAAcE,UAAU;gBAC9B,KAAK;oBAAS;wBACZ1G,YAAYpE,IAAI,CAAC;4BAAEY,QAAQlO,4BAA4BqY,QAAQ;wBAAC;wBAChE;oBACF;gBACA,KAAK;oBAAO;wBACV7K;wBAEA,SAAS8K,UACPC,SAAwC,EACxC9J,MAAsB;4BAEtB,KAAK,MAAMhB,YAAYgB,OAAOf,MAAM,GAAI;gCACtC,KAAK,MAAM,CAAC3C,KAAK6J,MAAM,IAAInH,SAAU;oCACnC,IAAImH,MAAMhH,QAAQ,KAAK,WAAW;oCAClC,IAAI2K,UAAUjM,GAAG,CAACvB,MAAM;oCAExB,MAAM8J,UAAUvS,YAAYsS;oCAE5B2D,UAAUlN,GAAG,CAACN,KAAK;wCACjB8J;wCACA2D,SAAS5D,MAAM6D,MAAM,GACjB9V,8BAA8BiS,MAAM6D,MAAM,IAC1CrT;oCACN;gCACF;4BACF;wBACF;wBAEA,MAAMsP,SAAS,IAAIjL;wBACnB6O,UAAU5D,QAAQvK;wBAElB,KAAK,MAAMkD,UAAUJ,QAAS;4BAC5B,MAAMY,QAAQX,aAAaxB,GAAG,CAAC2B;4BAC/B,IAAI,CAACQ,OAAO;gCACV;4BACF;4BAEA,MAAM6K,eAAe,IAAIjP,IAAIiL;4BAC7B4D,UAAUI,cAAc7K,MAAMC,YAAY;4BAE1CV,aAAaC,QAAQ;gCACnBa,QAAQlO,4BAA4B2Y,KAAK;gCACzCtD,MAAMjG,OAAO,EAAEpC;gCACf0H,QAAQ;uCAAIgE,aAAahL,MAAM;iCAAG;gCAClC0H,UAAU,EAAE;4BACd;wBACF;wBAEA,IAAIrI,kBAAkB;4BACpB,MAAM6L,OAAOV,cAAcW,KAAK,CAACC,QAAQ;4BACzC,MAAMC,cACJH,OAAO,OAAO,GAAG9U,KAAKkV,KAAK,CAACJ,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,GAAGA,KAAK,EAAE,CAAC;4BAC/D1Y,IAAImT,KAAK,CAAC,CAAC,YAAY,EAAE0F,aAAa;4BACtChM,mBAAmB;wBACrB;wBACA;oBACF;gBACA;YACF;QACF;IACF;IAEAkL,uBAAuBxF,KAAK,CAAC,CAACtN;QAC5BuN,QAAQ5I,KAAK,CAAC3E;QACd3B,QAAQuU,IAAI,CAAC;IACf;IAEA,OAAOrG;AACT", "ignoreList": [0]}