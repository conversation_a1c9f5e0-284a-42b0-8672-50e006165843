{"version": 3, "sources": ["../../src/lib/normalize-path.ts"], "sourcesContent": ["import path from 'path'\n\nexport function normalizePath(file: string) {\n  return path.sep === '\\\\' ? file.replace(/\\\\/g, '/') : file\n}\n"], "names": ["path", "normalizePath", "file", "sep", "replace"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,OAAO,SAASC,cAAcC,IAAY;IACxC,OAAOF,KAAKG,GAAG,KAAK,OAAOD,KAAKE,OAAO,CAAC,OAAO,OAAOF;AACxD", "ignoreList": [0]}