{"version": 3, "sources": ["../../../../src/server/app-render/module-loading/track-dynamic-import.ts"], "sourcesContent": ["import { InvariantError } from '../../../shared/lib/invariant-error'\nimport { isThenable } from '../../../shared/lib/is-thenable'\nimport { trackPendingImport } from './track-module-loading.external'\n\n/**\n * in DynamicIO, `import(...)` will be transformed into `trackDynamicImport(import(...))`.\n * A dynamic import is essentially a cached async function, except it's cached by the module system.\n *\n * The promises are tracked globally regardless of if the `import()` happens inside a render or outside of it.\n * When rendering, we can make the `cacheSignal` wait for all pending promises via `trackPendingModules`.\n * */\nexport function trackDynamicImport<TExports extends Record<string, any>>(\n  modulePromise: Promise<TExports>\n): Promise<TExports> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      \"Dynamic imports should not be instrumented in the edge runtime, because `dynamicIO` doesn't support it\"\n    )\n  }\n\n  if (!isThenable(modulePromise)) {\n    // We're expecting `import()` to always return a promise. If it's not, something's very wrong.\n    throw new InvariantError(\n      '`trackDynamicImport` should always receive a promise. Something went wrong in the dynamic imports transform.'\n    )\n  }\n\n  // Even if we're inside a prerender and have `workUnitStore.cacheSignal`, we always track the promise globally.\n  // (i.e. via the global `moduleLoadingSignal` that `trackPendingImport` uses internally).\n  //\n  // We do this because the `import()` promise might be cached in userspace:\n  // (which is quite common for e.g. lazy initialization in libraries)\n  //\n  //   let promise;\n  //   function doDynamicImportOnce() {\n  //     if (!promise) {\n  //       promise = import(\"...\");\n  //       // transformed into:\n  //       // promise = trackDynamicImport(import(\"...\"));\n  //     }\n  //     return promise;\n  //   }\n  //\n  // If multiple prerenders (e.g. multiple pages) depend on `doDynamicImportOnce`,\n  // we have to wait for the import *in all of them*.\n  // If we only tracked it using `workUnitStore.cacheSignal.trackRead()`,\n  // then only the first prerender to call `doDynamicImportOnce` would wait --\n  // Subsequent prerenders would re-use the existing `promise`,\n  // and `trackDynamicImport` wouldn't be called again in their scope,\n  // so their respective CacheSignals wouldn't wait for the promise.\n  trackPendingImport(modulePromise)\n\n  return modulePromise\n}\n"], "names": ["trackDynamicImport", "modulePromise", "process", "env", "NEXT_RUNTIME", "InvariantError", "isThenable", "trackPendingImport"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;gCAXe;4BACJ;4CACQ;AAS5B,SAASA,mBACdC,aAAgC;IAEhC,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,2GADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,CAACC,IAAAA,sBAAU,EAACL,gBAAgB;QAC9B,8FAA8F;QAC9F,MAAM,qBAEL,CAFK,IAAII,8BAAc,CACtB,iHADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,+GAA+G;IAC/G,yFAAyF;IACzF,EAAE;IACF,0EAA0E;IAC1E,oEAAoE;IACpE,EAAE;IACF,iBAAiB;IACjB,qCAAqC;IACrC,sBAAsB;IACtB,iCAAiC;IACjC,6BAA6B;IAC7B,wDAAwD;IACxD,QAAQ;IACR,sBAAsB;IACtB,MAAM;IACN,EAAE;IACF,gFAAgF;IAChF,mDAAmD;IACnD,uEAAuE;IACvE,4EAA4E;IAC5E,6DAA6D;IAC7D,oEAAoE;IACpE,kEAAkE;IAClEE,IAAAA,8CAAkB,EAACN;IAEnB,OAAOA;AACT", "ignoreList": [0]}