{"name": "@next/eslint-plugin-next", "version": "15.4.4", "description": "ESLint plugin for Next.js.", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "repository": {"url": "vercel/next.js", "directory": "packages/eslint-plugin-next"}, "files": ["dist"], "dependencies": {"fast-glob": "3.3.1"}, "devDependencies": {"eslint": "8.56.0"}, "scripts": {"dev": "pnpm build", "build": "swc -d dist src && pnpm types", "types": "tsc src/index.ts --skipLibCheck --declaration --emitDeclarationOnly --declarationDir dist", "prepublishOnly": "cd ../../ && turbo run build"}}