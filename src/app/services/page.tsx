import Link from 'next/link'

export default function Services() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Link href="/" className="text-2xl font-bold text-indigo-600">Conceal Carry NYC</Link>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/" className="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Home</Link>
                <Link href="/services" className="text-gray-900 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Services</Link>
                <Link href="/about" className="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">About</Link>
                <Link href="/contact" className="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Contact</Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
              Our Services
            </h1>
            <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
              Comprehensive concealed carry permit services for New York City residents
            </p>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          
          {/* Application Assistance */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500 text-white">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-lg font-medium text-gray-900 truncate">
                      Application Assistance
                    </dt>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Complete guidance through the NYC concealed carry permit application process, including:
                </p>
                <ul className="mt-2 text-sm text-gray-500 list-disc list-inside">
                  <li>Document preparation and review</li>
                  <li>Form completion assistance</li>
                  <li>Background check preparation</li>
                  <li>Interview preparation</li>
                  <li>Application submission support</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Training Programs */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500 text-white">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-lg font-medium text-gray-900 truncate">
                      Training Programs
                    </dt>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Professional firearms training and safety courses required for NYC permits:
                </p>
                <ul className="mt-2 text-sm text-gray-500 list-disc list-inside">
                  <li>16-hour basic firearms safety course</li>
                  <li>Live-fire training sessions</li>
                  <li>Legal use of force training</li>
                  <li>Situational awareness training</li>
                  <li>Ongoing proficiency training</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Legal Consultation */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500 text-white">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-lg font-medium text-gray-900 truncate">
                      Legal Consultation
                    </dt>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Expert legal advice on concealed carry laws and regulations:
                </p>
                <ul className="mt-2 text-sm text-gray-500 list-disc list-inside">
                  <li>NYC and NY State law guidance</li>
                  <li>Reciprocity and travel laws</li>
                  <li>Storage and transport requirements</li>
                  <li>Self-defense law education</li>
                  <li>Compliance consulting</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Renewal Services */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500 text-white">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-lg font-medium text-gray-900 truncate">
                      Renewal Services
                    </dt>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Ongoing support for permit holders:
                </p>
                <ul className="mt-2 text-sm text-gray-500 list-disc list-inside">
                  <li>Permit renewal assistance</li>
                  <li>Address change notifications</li>
                  <li>Lost permit replacement</li>
                  <li>Recertification training</li>
                  <li>Compliance updates</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Business Services */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500 text-white">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-lg font-medium text-gray-900 truncate">
                      Business Services
                    </dt>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Specialized services for business owners:
                </p>
                <ul className="mt-2 text-sm text-gray-500 list-disc list-inside">
                  <li>Business carry permits</li>
                  <li>Security consultation</li>
                  <li>Employee training programs</li>
                  <li>Premises license assistance</li>
                  <li>Risk assessment services</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Emergency Services */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500 text-white">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-lg font-medium text-gray-900 truncate">
                      Emergency Support
                    </dt>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Urgent assistance when you need it most:
                </p>
                <ul className="mt-2 text-sm text-gray-500 list-disc list-inside">
                  <li>Expedited application processing</li>
                  <li>Legal incident support</li>
                  <li>Emergency consultation</li>
                  <li>Court appearance assistance</li>
                  <li>24/7 legal hotline access</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-indigo-700">
        <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            <span className="block">Ready to begin?</span>
            <span className="block">Let's discuss your needs.</span>
          </h2>
          <p className="mt-4 text-lg leading-6 text-indigo-200">
            Contact us today for a free consultation about your concealed carry permit needs.
          </p>
          <Link
            href="/contact"
            className="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 sm:w-auto"
          >
            Schedule Free Consultation
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <div className="flex justify-center space-x-6 md:order-2">
            <p className="text-center text-sm text-gray-500">
              Professional concealed carry permit assistance in New York City
            </p>
          </div>
          <div className="mt-8 md:mt-0 md:order-1">
            <p className="text-center text-base text-gray-400">
              &copy; 2024 Conceal Carry NYC. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
